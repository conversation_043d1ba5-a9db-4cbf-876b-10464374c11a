[package]
name = "normans-quantum-proof-encryption"
version = "4.0.0"
edition = "2021"
authors = ["Norman <<EMAIL>>"]
description = "Normans Quantum-Proof Encryption Program with streaming support"
license = "MIT"

[dependencies]
# Cryptography
aes-gcm = "0.10"
argon2 = "0.5"
pqc_kyber = { version = "0.7", features = ["std"] }
sha3 = "0.10"
rand = "0.8"
rand_core = "0.6"
zeroize = { version = "1.7", features = ["derive"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
base64 = "0.22"
hex = "0.4"

# CLI and utilities
clap = { version = "4.5", features = ["derive"] }
anyhow = "1.0"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }

# File handling
zip = { version = "0.6", features = ["deflate"] }
tempfile = "3.10"
dirs = "6.0.0"
filetime = "0.2"

# Progress and UI
indicatif = "0.17"
colored = "3.0.0"
rpassword = "7.3"
ratatui = "0.29.0"
crossterm = "0.29.0"
tui-textarea = "0.7"

[dev-dependencies]
criterion = "0.6"
tempfile = "3.10"

[[bench]]
name = "encryption_benchmark"
harness = false

[[example]]
name = "demo"