# Quantum-Proof Encryption Tool - Usage Instructions

## Overview
This quantum-proof encryption tool provides military-grade encryption using post-quantum cryptography (Kyber) combined with AES-GCM. It can encrypt files, folders, texts, and media with both public-key and password-based encryption.

## Build Status
✅ **Project is now fully functional and ready to use!**

## Installation & Setup

### 1. Build the Project
```bash
cd "Encryptor Normans/quantum_encrypt"
cargo build --release
```

### 2. Binary Location
After building, the executable will be at:
```
./target/release/quantum-proof-encryption
```

## Basic Usage

### 1. Generate Quantum-Safe Key Pairs
```bash
# Generate a new key pair
./target/release/quantum-proof-encryption generate-keys \
    --pub-out my_public.key \
    --priv-out my_private.key

# Generate with custom security level (default is Standard)
./target/release/quantum-proof-encryption generate-keys \
    --pub-out my_public.key \
    --priv-out my_private.key \
    --security high
```

### 2. File Encryption

#### Using Public Key Encryption
```bash
# Encrypt a single file
./target/release/quantum-proof-encryption encrypt-file document.pdf \
    --public-key my_public.key \
    --output document.pdf.qpe

# Encrypt with progress bar
./target/release/quantum-proof-encryption encrypt-file large_file.zip \
    --public-key my_public.key \
    --output large_file.zip.qpe \
    --progress
```

#### Using Password Encryption
```bash
# Encrypt with password (will prompt for password)
./target/release/quantum-proof-encryption encrypt-file document.pdf \
    --password \
    --output document.pdf.qpe

# Or pipe password
echo "my_secure_password" | ./target/release/quantum-proof-encryption encrypt-file document.pdf \
    --password \
    --output document.pdf.qpe
```

### 3. File Decryption

#### Using Private Key
```bash
# Decrypt a file
./target/release/quantum-proof-encryption decrypt-file document.pdf.qpe \
    --private-key my_private.key \
    --output decrypted_document.pdf
```

#### Using Password
```bash
# Decrypt with password
./target/release/quantum-proof-encryption decrypt-file document.pdf.qpe \
    --password \
    --output decrypted_document.pdf
```

### 4. Folder Encryption
```bash
# Encrypt entire folder (creates compressed archive)
./target/release/quantum-proof-encryption encrypt-folder my_documents \
    --public-key my_public.key \
    --output my_documents.qpe

# Decrypt folder
./target/release/quantum-proof-encryption decrypt-folder my_documents.qpe \
    --private-key my_private.key \
    --output restored_documents
```

### 5. Text Encryption
```bash
# Encrypt text directly
./target/release/quantum-proof-encryption encrypt-text "Secret message!" \
    --public-key my_public.key

# Decrypt text
./target/release/quantum-proof-encryption decrypt-text "encrypted_base64_text" \
    --private-key my_private.key
```

### 6. Media Encryption
Media files are encrypted the same way as regular files:
```bash
# Encrypt video file
./target/release/quantum-proof-encryption encrypt-file movie.mp4 \
    --public-key my_public.key \
    --output movie.mp4.qpe \
    --progress

# Encrypt image
./target/release/quantum-proof-encryption encrypt-file photo.jpg \
    --password \
    --output photo.jpg.qpe
```

## Security Levels

The tool supports three security levels:
- **Low**: Kyber512 (fastest, good for most use cases)
- **Standard**: Kyber768 (balanced security/performance) - **Default**
- **High**: Kyber1024 (maximum security, slower)

```bash
# Use high security
./target/release/quantum-proof-encryption encrypt-file sensitive.doc \
    --public-key my_public.key \
    --security high \
    --output sensitive.doc.qpe
```

## Advanced Features

### Batch Operations
```bash
# Encrypt multiple files
for file in *.pdf; do
    ./target/release/quantum-proof-encryption encrypt-file "$file" \
        --public-key my_public.key \
        --output "${file}.qpe"
done
```

### Key Management
```bash
# Generate multiple key pairs for different purposes
./target/release/quantum-proof-encryption generate-keys \
    --pub-out work_public.key \
    --priv-out work_private.key

./target/release/quantum-proof-encryption generate-keys \
    --pub-out personal_public.key \
    --priv-out personal_private.key
```

## File Extensions
- Encrypted files use `.qpe` extension (Quantum Proof Encryption)
- Key files use `.key` extension
- Original file extensions are preserved during decryption

## Security Features
- ✅ Post-quantum cryptography (Kyber KEM)
- ✅ AES-256-GCM symmetric encryption
- ✅ Argon2 password hashing
- ✅ Secure random number generation
- ✅ Memory protection (zeroization)
- ✅ File integrity verification
- ✅ Metadata preservation

## Performance Tips
1. Use `--progress` flag for large files to monitor progress
2. For maximum performance, use "low" security level
3. For maximum security, use "high" security level
4. Folder encryption automatically compresses data

## Troubleshooting

### Common Issues
1. **Permission denied**: Ensure you have read/write permissions for input/output files
2. **File not found**: Check file paths are correct
3. **Invalid key**: Ensure you're using the correct public/private key pair
4. **Wrong password**: Password-encrypted files require the exact same password

### Getting Help
```bash
# General help
./target/release/quantum-proof-encryption --help

# Command-specific help
./target/release/quantum-proof-encryption encrypt-file --help
./target/release/quantum-proof-encryption generate-keys --help
```

## Example Workflow

1. **Setup**: Generate your key pair
2. **Encrypt**: Secure your important files
3. **Share**: Send encrypted files safely
4. **Decrypt**: Restore files when needed

```bash
# Complete example workflow
./target/release/quantum-proof-encryption generate-keys --pub-out alice.pub --priv-out alice.priv
./target/release/quantum-proof-encryption encrypt-file secret.txt --public-key alice.pub --output secret.txt.qpe
./target/release/quantum-proof-encryption decrypt-file secret.txt.qpe --private-key alice.priv --output restored_secret.txt
```

The tool is now ready for production use! 🔒
