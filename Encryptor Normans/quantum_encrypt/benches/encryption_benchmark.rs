use criterion::{black_box, criterion_group, criterion_main, Criterion, Throughput};
use normans_quantum_proof_encryption::{QuantumProofEncryption, SecurityLevel};
use std::fs;
use tempfile::TempDir;

fn benchmark_key_generation(c: &mut Criterion) {
    let mut group = c.benchmark_group("key_generation");
    
    for level in [SecurityLevel::Standard, SecurityLevel::High, SecurityLevel::Maximum] {
        group.bench_function(format!("{:?}", level), |b| {
            let qpe = QuantumProofEncryption::new(level);
            b.iter(|| {
                let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
                black_box((pub_key, priv_key));
            });
        });
    }
    
    group.finish();
}

fn benchmark_file_encryption(c: &mut Criterion) {
    let mut group = c.benchmark_group("file_encryption");
    let temp_dir = TempDir::new().unwrap();
    
    // Create test files of different sizes
    let sizes = [(1024, "1KB"), (1024 * 1024, "1MB"), (10 * 1024 * 1024, "10MB")];
    
    for (size, name) in sizes {
        let input_path = temp_dir.path().join(format!("test_{}.bin", name));
        let data: Vec<u8> = (0..size).map(|i| (i % 256) as u8).collect();
        fs::write(&input_path, &data).unwrap();
        
        let qpe = QuantumProofEncryption::new(SecurityLevel::High);
        let (pub_key, _priv_key) = qpe.generate_keypair().unwrap();
        
        group.throughput(Throughput::Bytes(size as u64));
        group.bench_function(format!("encrypt_{}", name), |b| {
            b.iter(|| {
                let output_path = temp_dir.path().join(format!("encrypted_{}.qpe", name));
                qpe.encrypt_file(&input_path, &output_path, Some(&pub_key), None, false)
                    .unwrap();
                fs::remove_file(output_path).unwrap();
            });
        });
    }
    
    group.finish();
}

fn benchmark_text_encryption(c: &mut Criterion) {
    let mut group = c.benchmark_group("text_encryption");
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);
    let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
    
    let texts = [
        ("short", "Hello, World!"),
        ("medium", "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."),
        ("long", &"x".repeat(1000)),
    ];
    
    for (name, text) in texts {
        group.bench_function(format!("encrypt_{}", name), |b| {
            b.iter(|| {
                let encrypted = qpe.encrypt_text(black_box(text), Some(&pub_key), None).unwrap();
                black_box(encrypted);
            });
        });
        
        let encrypted = qpe.encrypt_text(text, Some(&pub_key), None).unwrap();
        group.bench_function(format!("decrypt_{}", name), |b| {
            b.iter(|| {
                let decrypted = qpe.decrypt_text(black_box(&encrypted), Some(&priv_key), None).unwrap();
                black_box(decrypted);
            });
        });
    }
    
    group.finish();
}

fn benchmark_password_vs_publickey(c: &mut Criterion) {
    let mut group = c.benchmark_group("encryption_method_comparison");
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);
    let (pub_key, _) = qpe.generate_keypair().unwrap();
    let password = "benchmark-password";
    let text = "This is a test message for benchmarking different encryption methods.";
    
    group.bench_function("public_key_encryption", |b| {
        b.iter(|| {
            let encrypted = qpe.encrypt_text(black_box(text), Some(&pub_key), None).unwrap();
            black_box(encrypted);
        });
    });
    
    group.bench_function("password_encryption", |b| {
        b.iter(|| {
            let encrypted = qpe.encrypt_text(black_box(text), None, Some(password)).unwrap();
            black_box(encrypted);
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    benchmark_key_generation,
    benchmark_file_encryption,
    benchmark_text_encryption,
    benchmark_password_vs_publickey
);
criterion_main!(benches);