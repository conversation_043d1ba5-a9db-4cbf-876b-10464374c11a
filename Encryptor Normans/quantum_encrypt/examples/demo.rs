use normans_quantum_proof_encryption::{QuantumProofEncryption, SecurityLevel};
use std::fs;
use std::path::Path;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔐 Quantum-Proof Encryption Demo\n");

    // Initialize with high security
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);
    println!("✓ Initialized with HIGH security level\n");

    // 1. Generate a keypair
    println!("1️⃣  Generating quantum-safe keypair...");
    let (public_key, private_key) = qpe.generate_keypair()?;
    
    // Save keys
    let pub_path = Path::new("demo_public.key");
    let priv_path = Path::new("demo_private.key");
    
    qpe.save_keypair(
        &public_key,
        &private_key,
        pub_path,
        priv_path,
        Some("demo_password"), // Encrypt private key
    )?;
    println!("   ✓ Keys saved: {} and {}\n", pub_path.display(), priv_path.display());

    // 2. Encrypt a text message
    println!("2️⃣  Encrypting a text message...");
    let secret_message = "The quantum computer is in room 42!";
    let encrypted_text = qpe.encrypt_text(secret_message, Some(&public_key), None)?;
    println!("   Original: {}", secret_message);
    println!("   Encrypted: {}...\n", &encrypted_text[..50]);

    // 3. Decrypt the message
    println!("3️⃣  Decrypting the message...");
    let (loaded_private_key, _) = qpe.load_key(priv_path, Some("demo_password"))?;
    let decrypted_text = qpe.decrypt_text(&encrypted_text, Some(&loaded_private_key), None)?;
    println!("   Decrypted: {}\n", decrypted_text);

    // 4. Create and encrypt a file
    println!("4️⃣  Creating and encrypting a file...");
    let test_file = Path::new("demo_document.txt");
    let encrypted_file = Path::new("demo_document.txt.qpe");
    
    fs::write(test_file, "This is a confidential document!\nTop secret information here.")?;
    qpe.encrypt_file(test_file, encrypted_file, Some(&public_key), None, true)?;
    println!("   ✓ File encrypted: {}\n", encrypted_file.display());

    // 5. Decrypt the file
    println!("5️⃣  Decrypting the file...");
    let decrypted_file = Path::new("demo_document_decrypted.txt");
    qpe.decrypt_file(encrypted_file, decrypted_file, Some(&loaded_private_key), None, true)?;
    
    let content = fs::read_to_string(decrypted_file)?;
    println!("   ✓ File decrypted successfully!");
    println!("   Content: {}\n", content);

    // 6. Password-based encryption
    println!("6️⃣  Testing password-based encryption...");
    let password = "super-secret-password-123";
    let encrypted_pwd = qpe.encrypt_text("Password-protected message", None, Some(password))?;
    let decrypted_pwd = qpe.decrypt_text(&encrypted_pwd, None, Some(password))?;
    println!("   ✓ Password encryption works: {}\n", decrypted_pwd);

    // 7. Generate secure password
    println!("7️⃣  Generating secure passwords...");
    use normans_quantum_proof_encryption::generate_secure_password;
    
    let strong_password = generate_secure_password(24, true, false);
    let readable_password = generate_secure_password(16, false, true);
    
    println!("   Strong: {}", strong_password);
    println!("   Readable: {}\n", readable_password);

    // Cleanup
    println!("🧹 Cleaning up demo files...");
    for path in [pub_path, priv_path, test_file, encrypted_file, decrypted_file] {
        if path.exists() {
            fs::remove_file(path)?;
        }
    }
    
    println!("\n✅ Demo completed successfully!");
    println!("\n💡 Try the interactive UI with: cargo run -- --ui");

    Ok(())
}