use crate::errors::{EncryptionError, Result};
use crate::key_management::KeyManager;
use crate::models::*;
use crate::streaming::{encrypt_file as stream_encrypt_file, decrypt_file as stream_decrypt_file};
use crate::utils::{decode_base64, encode_base64, generate_random_bytes, get_file_permissions};
use chrono::{DateTime, Utc};
use indicatif::{ProgressBar, ProgressStyle};
use std::fs::{self, File};
use std::io::{self, BufReader, BufWriter, Read, Write};
use std::path::{Path, PathBuf};
use tempfile::NamedTempFile;
use filetime::{FileTime, set_file_mtime};
use zip::write::FileOptions;
use zip::{CompressionMethod, ZipArchive, ZipWriter};

pub struct QuantumProofEncryption {
    security_level: SecurityLevel,
    key_manager: <PERSON><PERSON><PERSON><PERSON>,
}

impl QuantumProofEncryption {
    /// Create a new instance with the specified security level
    pub fn new(security_level: SecurityLevel) -> Self {
        let key_manager = KeyManager::new(security_level);
        Self {
            security_level,
            key_manager,
        }
    }

    /// Generate a new quantum-safe keypair
    pub fn generate_keypair(&self) -> Result<(Vec<u8>, Vec<u8>)> {
        self.key_manager.generate_keypair()
    }

    /// Save keypair to files
    pub fn save_keypair(
        &self,
        public_key: &[u8],
        private_key: &[u8],
        pub_path: &Path,
        priv_path: &Path,
        key_password: Option<&str>,
    ) -> Result<()> {
        self.key_manager.save_keypair(public_key, private_key, pub_path, priv_path, key_password)
    }

    /// Load a key from file
    pub fn load_key(&self, key_path: &Path, key_password: Option<&str>) -> Result<(Vec<u8>, KeyMetadata)> {
        self.key_manager.load_key(key_path, key_password)
    }

    /// Encrypt a file with progress indication
    pub fn encrypt_file(
        &self,
        input_path: &Path,
        output_path: &Path,
        public_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        if !input_path.exists() {
            return Err(EncryptionError::FileNotFound(
                input_path.to_string_lossy().to_string(),
            ));
        }

        let file_size = fs::metadata(input_path)?.len();
        
        if show_progress {
            let pb = ProgressBar::new(file_size);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );

            // Use a custom streaming encryptor with progress callback
            let encryptor = crate::streaming::StreamingEncryptor::new(self.security_level)
                .with_progress_callback(move |processed, _total| {
                    pb.set_position(processed);
                });

            let metadata = fs::metadata(input_path)?;
            let file_metadata = FileMetadata {
                original_name: input_path.file_name()
                    .ok_or_else(|| EncryptionError::FileNotFound("Invalid file name".to_string()))?
                    .to_string_lossy()
                    .to_string(),
                original_size: metadata.len(),
                modified: DateTime::<Utc>::from(metadata.modified()?),
                permissions: get_file_permissions(input_path)?,
            };

            let input_file = File::open(input_path)?;
            let output_file = File::create(output_path)?;
            let mut reader = BufReader::new(input_file);
            let mut writer = BufWriter::new(output_file);

            if let Some(pub_key) = public_key {
                encryptor.encrypt_stream_public_key(&mut reader, &mut writer, file_metadata, pub_key)?;
            } else if let Some(pwd) = password {
                encryptor.encrypt_stream_password(&mut reader, &mut writer, file_metadata, pwd)?;
            } else {
                return Err(EncryptionError::Key("Either public key or password must be provided".to_string()));
            }

            pb.finish_with_message("Encryption complete");
        } else {
            stream_encrypt_file(input_path, output_path, self.security_level, public_key, password)?;
        }

        Ok(())
    }

    /// Decrypt a file with progress indication
    pub fn decrypt_file(
        &self,
        input_path: &Path,
        output_path: &Path,
        private_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        if !input_path.exists() {
            return Err(EncryptionError::FileNotFound(
                input_path.to_string_lossy().to_string(),
            ));
        }

        if show_progress {
            let file_size = fs::metadata(input_path)?.len();
            let pb = ProgressBar::new(file_size);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );

            // Use custom streaming decryptor with progress
            let decryptor = crate::streaming::StreamingEncryptor::new(self.security_level)
                .with_progress_callback(move |processed, _total| {
                    pb.set_position(processed);
                });

            let input_file = File::open(input_path)?;
            let output_file = File::create(output_path)?;
            let mut reader = BufReader::new(input_file);
            let mut writer = BufWriter::new(output_file);

            let header = if let Some(priv_key) = private_key {
                decryptor.decrypt_stream_private_key(&mut reader, &mut writer, priv_key)?
            } else if let Some(pwd) = password {
                decryptor.decrypt_stream_password(&mut reader, &mut writer, pwd)?
            } else {
                return Err(EncryptionError::Key("Either private key or password must be provided".to_string()));
            };

            pb.finish_with_message("Decryption complete");

            // Restore metadata
            drop(writer);
            let mtime: i64 = header.file_metadata.modified.timestamp();
            let file_time = FileTime::from_unix_time(mtime, 0);
            let _ = set_file_mtime(output_path, file_time);

            #[cfg(unix)]
            if let Some(perms) = header.file_metadata.permissions {
                if let Ok(mode) = u32::from_str_radix(&perms, 8) {
                    crate::utils::set_file_permissions(output_path, mode)?;
                }
            }
        } else {
            stream_decrypt_file(input_path, output_path, self.security_level, private_key, password)?;
        }

        Ok(())
    }

    /// Encrypt a folder into an encrypted archive
    pub fn encrypt_folder(
        &self,
        folder_path: &Path,
        output_path: &Path,
        public_key: Option<&[u8]>,
        password: Option<&str>,
        compression_level: Option<i32>,
        show_progress: bool,
    ) -> Result<()> {
        if !folder_path.is_dir() {
            return Err(EncryptionError::FileNotFound(
                format!("'{}' is not a directory", folder_path.display()),
            ));
        }

        // Create temporary zip file
        let temp_zip = NamedTempFile::new()?;
        let temp_path = temp_zip.path().to_path_buf();

        {
            let file = File::create(&temp_path)?;
            let mut zip = ZipWriter::new(file);

            let options = FileOptions::default()
                .compression_method(CompressionMethod::Deflated);

            let options = if let Some(level) = compression_level {
                options.compression_level(Some(level))
            } else {
                options
            };

            // Collect all files
            let mut files = Vec::new();
            collect_files(folder_path, folder_path, &mut files)?;

            if show_progress {
                let pb = ProgressBar::new(files.len() as u64);
                pb.set_style(
                    ProgressStyle::default_bar()
                        .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} files")
                        .unwrap(),
                );

                for (relative_path, full_path) in &files {
                    zip.start_file(relative_path.to_string_lossy(), options)?;
                    let mut file = File::open(full_path)?;
                    io::copy(&mut file, &mut zip)?;
                    pb.inc(1);
                }

                pb.finish_with_message("Archive created");
            } else {
                for (relative_path, full_path) in &files {
                    zip.start_file(relative_path.to_string_lossy(), options)?;
                    let mut file = File::open(full_path)?;
                    io::copy(&mut file, &mut zip)?;
                }
            }

            zip.finish()?;
        }

        // Encrypt the zip file
        self.encrypt_file(&temp_path, output_path, public_key, password, show_progress)?;

        // Secure deletion of temp file is handled by NamedTempFile automatically
        Ok(())
    }

    /// Decrypt an encrypted folder archive
    pub fn decrypt_folder(
        &self,
        input_path: &Path,
        output_folder: &Path,
        private_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        // Create output directory
        fs::create_dir_all(output_folder)?;

        // Create temporary file for decrypted zip
        let temp_zip = NamedTempFile::new()?;
        let temp_path = temp_zip.path().to_path_buf();

        // Decrypt to temporary file
        self.decrypt_file(input_path, &temp_path, private_key, password, show_progress)?;

        // Extract zip file
        let file = File::open(&temp_path)?;
        let mut archive = ZipArchive::new(file)?;

        if show_progress {
            let pb = ProgressBar::new(archive.len() as u64);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} files")
                    .unwrap(),
            );

            for i in 0..archive.len() {
                let mut file = archive.by_index(i)?;
                let outpath = output_folder.join(file.sanitized_name());

                if file.is_dir() {
                    fs::create_dir_all(&outpath)?;
                } else {
                    if let Some(p) = outpath.parent() {
                        if !p.exists() {
                            fs::create_dir_all(p)?;
                        }
                    }
                    let mut outfile = File::create(&outpath)?;
                    io::copy(&mut file, &mut outfile)?;
                }

                // Set permissions on Unix
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    if let Some(mode) = file.unix_mode() {
                        fs::set_permissions(&outpath, fs::Permissions::from_mode(mode))?;
                    }
                }

                pb.inc(1);
            }

            pb.finish_with_message("Extraction complete");
        } else {
            for i in 0..archive.len() {
                let mut file = archive.by_index(i)?;
                let outpath = output_folder.join(file.sanitized_name());

                if file.is_dir() {
                    fs::create_dir_all(&outpath)?;
                } else {
                    if let Some(p) = outpath.parent() {
                        if !p.exists() {
                            fs::create_dir_all(p)?;
                        }
                    }
                    let mut outfile = File::create(&outpath)?;
                    io::copy(&mut file, &mut outfile)?;
                }

                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    if let Some(mode) = file.unix_mode() {
                        fs::set_permissions(&outpath, fs::Permissions::from_mode(mode))?;
                    }
                }
            }
        }

        Ok(())
    }

    /// Encrypt text to base64
    pub fn encrypt_text(
        &self,
        text: &str,
        public_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        let plaintext = text.as_bytes();
        let mut output = Vec::new();

        let metadata = FileMetadata {
            original_name: "text".to_string(),
            original_size: plaintext.len() as u64,
            modified: Utc::now(),
            permissions: None,
        };

        let encryptor = crate::streaming::StreamingEncryptor::new(self.security_level);
        
        if let Some(pub_key) = public_key {
            encryptor.encrypt_stream_public_key(plaintext, &mut output, metadata, pub_key)?;
        } else if let Some(pwd) = password {
            encryptor.encrypt_stream_password(plaintext, &mut output, metadata, pwd)?;
        } else {
            return Err(EncryptionError::Key("Either public key or password required".to_string()));
        }

        Ok(encode_base64(&output))
    }

    /// Decrypt base64 text
    pub fn decrypt_text(
        &self,
        encrypted_text: &str,
        private_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        let encrypted_bytes = decode_base64(encrypted_text.trim())?;
        let mut input = io::Cursor::new(encrypted_bytes);
        let mut output = Vec::new();

        let decryptor = crate::streaming::StreamingEncryptor::new(self.security_level);
        
        if let Some(priv_key) = private_key {
            decryptor.decrypt_stream_private_key(&mut input, &mut output, priv_key)?;
        } else if let Some(pwd) = password {
            decryptor.decrypt_stream_password(&mut input, &mut output, pwd)?;
        } else {
            return Err(EncryptionError::Key("Either private key or password required".to_string()));
        }

        String::from_utf8(output)
            .map_err(|_| EncryptionError::Decryption("Invalid UTF-8 in decrypted text".to_string()))
    }
}

/// Recursively collect files in a directory
fn collect_files(
    base_path: &Path,
    current_path: &Path,
    files: &mut Vec<(PathBuf, PathBuf)>,
) -> Result<()> {
    for entry in fs::read_dir(current_path)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_dir() {
            collect_files(base_path, &path, files)?;
        } else {
            let relative_path = path.strip_prefix(base_path)
                .map_err(|_| EncryptionError::Io(io::Error::new(
                    io::ErrorKind::Other,
                    "Failed to get relative path",
                )))?
                .to_path_buf();
            files.push((relative_path, path));
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_text_encryption() {
        let qpe = QuantumProofEncryption::new(SecurityLevel::Standard);
        let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
        
        let plaintext = "Hello, quantum world!";
        let encrypted = qpe.encrypt_text(plaintext, Some(&pub_key), None).unwrap();
        let decrypted = qpe.decrypt_text(&encrypted, Some(&priv_key), None).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_password_encryption() {
        let qpe = QuantumProofEncryption::new(SecurityLevel::High);
        let password = "super-secret-password";
        
        let plaintext = "Test password encryption";
        let encrypted = qpe.encrypt_text(plaintext, None, Some(password)).unwrap();
        let decrypted = qpe.decrypt_text(&encrypted, None, Some(password)).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
}