use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum EncryptionError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Base64 decode error: {0}")]
    Base64Decode(#[from] base64::DecodeError),
    
    #[error("Encryption error: {0}")]
    Encryption(String),
    
    #[error("Decryption error: {0}")]
    Decryption(String),
    
    #[error("Key error: {0}")]
    Key(String),
    
    #[error("Password error: {0}")]
    Password(String),
    
    #[error("Invalid security level: {0}")]
    InvalidSecurityLevel(String),
    
    #[error("File not found: {0}")]
    FileNotFound(String),
    
    #[error("Invalid format version: expected {expected}, got {got}")]
    InvalidFormatVersion { expected: String, got: String },
    
    #[error("Corrupted data: {0}")]
    CorruptedData(String),
    
    #[error("Verification failed: {0}")]
    VerificationFailed(String),
    
    #[error("Unsupported algorithm: {0}")]
    UnsupportedAlgorithm(String),
    
    #[error("ZIP error: {0}")]
    Zip(#[from] zip::result::ZipError),
    
    #[error("Argon2 error: {0}")]
    Argon2(#[from] argon2::Error),
}

pub type Result<T> = std::result::Result<T, EncryptionError>;