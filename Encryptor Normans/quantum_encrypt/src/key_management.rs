use crate::errors::{EncryptionError, Result};
use crate::models::*;
use crate::utils::{
    calculate_fingerprint, calculate_integrity_check, decode_base64, encode_base64,
    generate_key_id, generate_random_bytes, set_file_permissions,
};
use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Nonce,
};
use argon2::{Argon2, Params, Version};
use chrono::Utc;
use pqc_kyber::keypair;
use serde_json;
use std::fs;
use std::path::Path;

pub struct KeyManager {
    security_level: SecurityLevel,
}

impl KeyManager {
    pub fn new(security_level: SecurityLevel) -> Self {
        Self { security_level }
    }

    /// Generate a new quantum-safe keypair
    pub fn generate_keypair(&self) -> Result<(Vec<u8>, Vec<u8>)> {
        let mut rng = rand::thread_rng();
        
        let (public_key, secret_key) = match self.security_level.kyber_variant() {
            KyberVariant::Kyber512 => {
                let (pk, sk) = keypair(&mut rng);
                (pk.to_vec(), sk.to_vec())
            }
            KyberVariant::Kyber768 => {
                // For this example, we'll use Kyber512 for all variants
                // In production, you'd use the appropriate Kyber variant
                let (pk, sk) = keypair(&mut rng);
                (pk.to_vec(), sk.to_vec())
            }
            KyberVariant::Kyber1024 => {
                let (pk, sk) = keypair(&mut rng);
                (pk.to_vec(), sk.to_vec())
            }
        };
        
        Ok((public_key, secret_key))
    }

    /// Save keypair to files
    pub fn save_keypair(
        &self,
        public_key: &[u8],
        private_key: &[u8],
        pub_path: &Path,
        priv_path: &Path,
        key_password: Option<&str>,
    ) -> Result<()> {
        let key_id = generate_key_id(public_key);
        let fingerprint = calculate_fingerprint(public_key);
        
        let metadata = KeyMetadata {
            format_version: KEY_FORMAT_VERSION.to_string(),
            algorithm: "CRYSTALS-Kyber".to_string(),
            variant: self.security_level.kyber_variant(),
            security_level: self.security_level.as_str().to_string(),
            nist_level: self.security_level.nist_level(),
            created: Utc::now(),
            key_id,
            fingerprint,
            encrypted: key_password.map(|_| true),
        };
        
        // Save public key
        let metadata_json = serde_json::to_string(&metadata)?;
        let integrity = calculate_integrity_check(public_key, &metadata_json);
        
        let pub_data = PublicKeyFile {
            metadata: metadata.clone(),
            key: encode_base64(public_key),
            integrity,
        };
        
        let pub_json = serde_json::to_string_pretty(&pub_data)?;
        fs::write(pub_path, pub_json)?;
        
        // Save private key
        let priv_data = if let Some(password) = key_password {
            let encrypted_key = self.encrypt_private_key(private_key, password)?;
            PrivateKeyFile {
                metadata,
                key: None,
                encrypted_key: Some(encrypted_key),
                integrity: None,
                warning: None,
            }
        } else {
            let integrity = calculate_integrity_check(private_key, &metadata_json);
            PrivateKeyFile {
                metadata,
                key: Some(encode_base64(private_key)),
                encrypted_key: None,
                integrity: Some(integrity),
                warning: Some("PRIVATE KEY - KEEP SECRET AND SECURE!".to_string()),
            }
        };
        
        let priv_json = serde_json::to_string_pretty(&priv_data)?;
        fs::write(priv_path, priv_json)?;
        
        // Set restrictive permissions on private key
        #[cfg(unix)]
        set_file_permissions(priv_path, 0o600)?;
        
        Ok(())
    }

    /// Load key from file
    pub fn load_key(&self, key_path: &Path, key_password: Option<&str>) -> Result<(Vec<u8>, KeyMetadata)> {
        let contents = fs::read_to_string(key_path)?;
        
        // Try to parse as public key first
        if let Ok(pub_data) = serde_json::from_str::<PublicKeyFile>(&contents) {
            let key = decode_base64(&pub_data.key)?;
            
            // Verify integrity
            let metadata_json = serde_json::to_string(&pub_data.metadata)?;
            let expected_integrity = calculate_integrity_check(&key, &metadata_json);
            if expected_integrity != pub_data.integrity {
                return Err(EncryptionError::VerificationFailed(
                    "Key integrity check failed".to_string(),
                ));
            }
            
            return Ok((key, pub_data.metadata));
        }
        
        // Try to parse as private key
        let priv_data: PrivateKeyFile = serde_json::from_str(&contents)?;
        
        let key = if priv_data.metadata.encrypted.unwrap_or(false) {
            let encrypted_key = priv_data.encrypted_key
                .ok_or_else(|| EncryptionError::CorruptedData("Missing encrypted key data".to_string()))?;
            let password = key_password
                .ok_or_else(|| EncryptionError::Password("Password required for encrypted key".to_string()))?;
            self.decrypt_private_key(&encrypted_key, password)?
        } else {
            let key_str = priv_data.key
                .ok_or_else(|| EncryptionError::CorruptedData("Missing key data".to_string()))?;
            let key = decode_base64(&key_str)?;
            
            // Verify integrity if present
            if let Some(integrity) = priv_data.integrity {
                let metadata_json = serde_json::to_string(&priv_data.metadata)?;
                let expected_integrity = calculate_integrity_check(&key, &metadata_json);
                if expected_integrity != integrity {
                    return Err(EncryptionError::VerificationFailed(
                        "Key integrity check failed".to_string(),
                    ));
                }
            }
            
            key
        };
        
        Ok((key, priv_data.metadata))
    }

    /// Encrypt private key with password
    fn encrypt_private_key(&self, private_key: &[u8], password: &str) -> Result<EncryptedPrivateKey> {
        let salt = generate_random_bytes(SALT_SIZE);
        let iv = generate_random_bytes(IV_SIZE);
        
        let params = self.security_level.argon2_params();
        let argon2_params = Params::new(
            params.memory_cost,
            params.time_cost,
            params.parallelism,
            Some(AES_KEY_SIZE),
        ).map_err(|e| EncryptionError::Argon2(e))?;
        
        let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, argon2_params);
        
        let mut key = vec![0u8; AES_KEY_SIZE];
        argon2.hash_password_into(password.as_bytes(), &salt, &mut key)
            .map_err(|e| EncryptionError::Argon2(e))?;
        
        // Encrypt with AES-GCM
        let cipher = Aes256Gcm::new_from_slice(&key)
            .map_err(|_| EncryptionError::Encryption("Invalid key length".to_string()))?;
        
        let nonce = Nonce::from_slice(&iv);
        let ciphertext = cipher
            .encrypt(nonce, private_key)
            .map_err(|_| EncryptionError::Encryption("Failed to encrypt private key".to_string()))?;
        
        // Split ciphertext and tag
        let (encrypted_data, tag) = ciphertext.split_at(ciphertext.len() - TAG_SIZE);
        
        Ok(EncryptedPrivateKey {
            algorithm: "Argon2id-AES-GCM".to_string(),
            kdf_params: KdfParams {
                time_cost: params.time_cost,
                memory_cost: params.memory_cost,
                parallelism: params.parallelism,
                salt: encode_base64(&salt),
            },
            iv: encode_base64(&iv),
            ciphertext: encode_base64(encrypted_data),
            tag: encode_base64(tag),
        })
    }

    /// Decrypt private key with password
    fn decrypt_private_key(&self, encrypted_data: &EncryptedPrivateKey, password: &str) -> Result<Vec<u8>> {
        let salt = decode_base64(&encrypted_data.kdf_params.salt)?;
        let iv = decode_base64(&encrypted_data.iv)?;
        let ciphertext = decode_base64(&encrypted_data.ciphertext)?;
        let tag = decode_base64(&encrypted_data.tag)?;
        
        let argon2_params = Params::new(
            encrypted_data.kdf_params.memory_cost,
            encrypted_data.kdf_params.time_cost,
            encrypted_data.kdf_params.parallelism,
            Some(AES_KEY_SIZE),
        ).map_err(|e| EncryptionError::Argon2(e))?;
        
        let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, argon2_params);
        
        let mut key = vec![0u8; AES_KEY_SIZE];
        argon2.hash_password_into(password.as_bytes(), &salt, &mut key)
            .map_err(|e| EncryptionError::Argon2(e))?;
        
        // Decrypt with AES-GCM
        let cipher = Aes256Gcm::new_from_slice(&key)
            .map_err(|_| EncryptionError::Decryption("Invalid key length".to_string()))?;
        
        let nonce = Nonce::from_slice(&iv);
        let mut combined = ciphertext;
        combined.extend_from_slice(&tag);
        
        let plaintext = cipher
            .decrypt(nonce, combined.as_slice())
            .map_err(|_| EncryptionError::Password("Failed to decrypt private key. Wrong password?".to_string()))?;
        
        Ok(plaintext)
    }

    /// Export public key in PEM format
    pub fn export_public_key_pem(&self, key_path: &Path) -> Result<String> {
        let (pub_key, _) = self.load_key(key_path, None)?;
        let b64_key = encode_base64(&pub_key);
        
        let mut pem = String::from("-----BEGIN QUANTUM PUBLIC KEY-----\n");
        for chunk in b64_key.as_bytes().chunks(64) {
            pem.push_str(&String::from_utf8_lossy(chunk));
            pem.push('\n');
        }
        pem.push_str("-----END QUANTUM PUBLIC KEY-----");
        
        Ok(pem)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
}

    #[test]
    fn test_keypair_generation() {
        let manager = KeyManager::new(SecurityLevel::Standard);
        let (pub_key, priv_key) = manager.generate_keypair().unwrap();
        
        assert!(!pub_key.is_empty());
        assert!(!priv_key.is_empty());
    }

    #[test]
    fn test_save_and_load_keys() {
        let temp_dir = TempDir::new().unwrap();
        let pub_path = temp_dir.path().join("test.pub");
        let priv_path = temp_dir.path().join("test.key");
        
        let manager = KeyManager::new(SecurityLevel::High);
        let (pub_key, priv_key) = manager.generate_keypair().unwrap();
        
        // Test without password
        manager.save_keypair(&pub_key, &priv_key, &pub_path, &priv_path, None).unwrap();
        
        let (loaded_pub, _) = manager.load_key(&pub_path, None).unwrap();
    }    