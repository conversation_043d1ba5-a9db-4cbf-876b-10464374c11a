//! # Quantum-Proof Encryption Library
//!
//! A comprehensive post-quantum cryptography library implementing CRYSTALS-Kyber
//! for key encapsulation and AES-256-GCM for symmetric encryption.
//!
//! ## Features
//!
//! - **Post-Quantum Security**: Uses CRYSTALS-Kyber (ML-KEM) for quantum-resistant key encapsulation
//! - **Streaming Support**: Efficient handling of large files without loading into memory
//! - **Multiple Security Levels**: Standard (NIST Level 1), High (NIST Level 3), Maximum (NIST Level 5)
//! - **Flexible Encryption**: Support for both public-key and password-based encryption
//! - **Comprehensive File Support**: Encrypt individual files, folders, and text
//! - **Key Management**: Secure key generation, storage, and password protection
//!
//! ## Example
//!
//! ```rust,no_run
//! use quantum_proof_encryption::{QuantumProofEncryption, SecurityLevel};
//! use std::path::Path;
//!
//! // Create encryption instance
//! let qpe = QuantumProofEncryption::new(SecurityLevel::High);
//!
//! // Generate keypair
//! let (public_key, private_key) = qpe.generate_keypair().unwrap();
//!
//! // Save keys
//! qpe.save_keypair(
//!     &public_key,
//!     &private_key,
//!     Path::new("alice.pub"),
//!     Path::new("alice.key"),
//!     Some("my-secure-password")
//! ).unwrap();
//!
//! // Encrypt a file
//! qpe.encrypt_file(
//!     Path::new("document.pdf"),
//!     Path::new("document.pdf.qpe"),
//!     Some(&public_key),
//!     None,
//!     true
//! ).unwrap();
//! ```

pub mod errors;
pub mod models;
pub mod utils;
pub mod key_management;
pub mod streaming;
pub mod encryption;
pub mod ui;

// Re-export main types and functions
pub use crate::encryption::QuantumProofEncryption;
pub use crate::errors::{EncryptionError, Result};
pub use crate::key_management::KeyManager;
pub use crate::models::{SecurityLevel, KeyMetadata, SecureBytes};
pub use crate::utils::{generate_secure_password, print_banner};

/// Library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// Get library information
pub fn info() -> String {
    format!(
        "Quantum-Proof Encryption Library v{}\n\
         Post-quantum cryptography using CRYSTALS-Kyber and AES-256-GCM\n\
         Supports streaming encryption for large files",
        VERSION
    )
}

#[cfg(test)]
mod integration_tests {
    use super::*;
    use std::fs;
    use std::path::Path;
    use tempfile::TempDir;

    #[test]
    fn test_full_encryption_workflow() {
        let temp_dir = TempDir::new().unwrap();
        let pub_path = temp_dir.path().join("test.pub");
        let priv_path = temp_dir.path().join("test.key");
        let input_path = temp_dir.path().join("test.txt");
        let encrypted_path = temp_dir.path().join("test.txt.qpe");
        let decrypted_path = temp_dir.path().join("test.decrypted.txt");

        // Create test file
        let test_content = "This is a test file for quantum-proof encryption!";
        fs::write(&input_path, test_content).unwrap();

        // Initialize encryption
        let qpe = QuantumProofEncryption::new(SecurityLevel::High);

        // Generate and save keypair
        let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
        qpe.save_keypair(&pub_key, &priv_key, &pub_path, &priv_path, Some("test123"))
            .unwrap();

        // Load keys
        let (loaded_pub, _) = qpe.load_key(&pub_path, None).unwrap();
        let (loaded_priv, _) = qpe.load_key(&priv_path, Some("test123")).unwrap();

        // Encrypt file
        qpe.encrypt_file(&input_path, &encrypted_path, Some(&loaded_pub), None, false)
            .unwrap();

        // Verify encrypted file exists and is different
        assert!(encrypted_path.exists());
        let encrypted_content = fs::read(&encrypted_path).unwrap();
        assert_ne!(encrypted_content, test_content.as_bytes());

        // Decrypt file
        qpe.decrypt_file(&encrypted_path, &decrypted_path, Some(&loaded_priv), None, false)
            .unwrap();

        // Verify decrypted content matches
        let decrypted_content = fs::read_to_string(&decrypted_path).unwrap();
        assert_eq!(decrypted_content, test_content);
    }

    #[test]
    fn test_password_based_encryption() {
        let temp_dir = TempDir::new().unwrap();
        let input_path = temp_dir.path().join("test.txt");
        let encrypted_path = temp_dir.path().join("test.txt.qpe");
        let decrypted_path = temp_dir.path().join("test.decrypted.txt");

        // Create test file
        let test_content = "Password-based encryption test";
        fs::write(&input_path, test_content).unwrap();

        let qpe = QuantumProofEncryption::new(SecurityLevel::Standard);

        // Encrypt with password
        qpe.encrypt_file(&input_path, &encrypted_path, None, Some("my-password"), false)
            .unwrap();

        // Decrypt with password
        qpe.decrypt_file(&encrypted_path, &decrypted_path, None, Some("my-password"), false)
            .unwrap();

        // Verify
        let decrypted_content = fs::read_to_string(&decrypted_path).unwrap();
        assert_eq!(decrypted_content, test_content);
    }

    #[test]
    fn test_wrong_password_fails() {
        let qpe = QuantumProofEncryption::new(SecurityLevel::High);
        
        let encrypted = qpe.encrypt_text("Secret message", None, Some("correct-password"))
            .unwrap();
        
        let result = qpe.decrypt_text(&encrypted, None, Some("wrong-password"));
        assert!(result.is_err());
    }
}