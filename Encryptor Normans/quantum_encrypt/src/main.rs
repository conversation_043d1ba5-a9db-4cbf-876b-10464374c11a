use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::*;
use normans_quantum_proof_encryption::{
    QuantumProofEncryption, SecurityLevel,
    generate_secure_password, print_banner, ui::App
};
use rpassword::prompt_password;
use std::fs;
use std::path::PathBuf;

#[derive(Parser)]
#[command(
    name = "normans-qpe",
    version = "4.0.0",
    about = "Normans Quantum-Proof Encryption Program - Version 4.0 Streaming Edition",
    long_about = None
)]
struct Cli {
    /// Security level: standard, high, or maximum
    #[arg(short, long, default_value = "high")]
    security: String,

    /// Enable quiet mode (minimal output)
    #[arg(short, long)]
    quiet: bool,

    /// Launch Terminal UI mode
    #[arg(long)]
    ui: bool,

    #[command(subcommand)]
    command: Option<Commands>,
}

#[derive(Subcommand)]
enum Commands {
    /// Generate a new quantum-safe keypair
    GenerateKeys {
        /// Public key output path
        #[arg(long, default_value = "public.key")]
        pub_out: PathBuf,

        /// Private key output path
        #[arg(long, default_value = "private.key")]
        priv_out: PathBuf,

        /// Encrypt private key with password
        #[arg(long)]
        encrypt_private_key: bool,
    },

    /// Encrypt one or more files
    EncryptFile {
        /// Files to encrypt
        files: Vec<PathBuf>,

        /// Output file path (for single file)
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Output directory (for multiple files)
        #[arg(long)]
        output_dir: Option<PathBuf>,

        /// Public key file for encryption
        #[arg(long)]
        public_key: Option<PathBuf>,

        /// Use password-based encryption
        #[arg(long)]
        password: bool,
    },

    /// Decrypt one or more files
    DecryptFile {
        /// Files to decrypt
        files: Vec<PathBuf>,

        /// Output file path (for single file)
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Output directory (for multiple files)
        #[arg(long)]
        output_dir: Option<PathBuf>,

        /// Private key file for decryption
        #[arg(long)]
        private_key: Option<PathBuf>,

        /// Use password for decryption
        #[arg(long)]
        password: bool,

        /// Private key is password-protected
        #[arg(long)]
        key_password: bool,
    },

    /// Encrypt a folder into a single archive
    EncryptFolder {
        /// Folder to encrypt
        folder: PathBuf,

        /// Output file path
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Public key file for encryption
        #[arg(long)]
        public_key: Option<PathBuf>,

        /// Use password-based encryption
        #[arg(long)]
        password: bool,

        /// Compression level (0-9)
        #[arg(long, default_value = "6")]
        compression: i32,
    },

    /// Decrypt a folder archive
    DecryptFolder {
        /// Archive to decrypt
        archive: PathBuf,

        /// Output folder path
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Private key file for decryption
        #[arg(long)]
        private_key: Option<PathBuf>,

        /// Use password for decryption
        #[arg(long)]
        password: bool,

        /// Private key is password-protected
        #[arg(long)]
        key_password: bool,
    },

    /// Encrypt text
    EncryptText {
        /// Text to encrypt
        text: String,

        /// Public key file for encryption
        #[arg(long)]
        public_key: Option<PathBuf>,

        /// Use password-based encryption
        #[arg(long)]
        password: bool,
    },

    /// Decrypt text
    DecryptText {
        /// Base64 encrypted text
        text: String,

        /// Private key file for decryption
        #[arg(long)]
        private_key: Option<PathBuf>,

        /// Use password for decryption
        #[arg(long)]
        password: bool,

        /// Private key is password-protected
        #[arg(long)]
        key_password: bool,
    },

    /// Display key information
    KeyInfo {
        /// Key file to inspect
        key_file: PathBuf,
    },

    /// Generate a secure password
    GeneratePassword {
        /// Password length
        #[arg(long, default_value = "32")]
        length: usize,

        /// Exclude symbols
        #[arg(long)]
        no_symbols: bool,

        /// Generate human-readable password
        #[arg(long)]
        readable: bool,
    },
}

fn main() -> Result<()> {
    let cli = Cli::parse();

    // Launch UI mode if requested
    if cli.ui || cli.command.is_none() {
        let mut app = App::new();
        return app.run_tui().map_err(|e| anyhow::anyhow!("UI error: {}", e));
    }

    // Parse security level
    let security_level = SecurityLevel::from_str(&cli.security)
        .map_err(|e| anyhow::anyhow!("Invalid security level: {}", e))?;

    // Show banner for non-quiet operations
    if !cli.quiet {
        let cmd = cli.command.as_ref().unwrap();
        if !matches!(cmd, Commands::GeneratePassword { .. } | Commands::KeyInfo { .. }) {
            print_banner();
        }
    }

    let qpe = QuantumProofEncryption::new(security_level);

    match cli.command.unwrap() {
        Commands::GenerateKeys { pub_out, priv_out, encrypt_private_key } => {
            let (pub_key, priv_key) = qpe.generate_keypair()?;
            
            let key_password = if encrypt_private_key {
                let password = prompt_password("Enter password to encrypt private key: ")?;
                let confirm = prompt_password("Confirm password: ")?;
                if password != confirm {
                    anyhow::bail!("Passwords do not match");
                }
                Some(password)
            } else {
                None
            };

            qpe.save_keypair(
                &pub_key,
                &priv_key,
                &pub_out,
                &priv_out,
                key_password.as_deref(),
            )?;

            if !cli.quiet {
                println!("{} Keys generated successfully!", "✓".green());
                println!("  Public key: {}", pub_out.display());
                println!("  Private key: {}", priv_out.display());
                
                let (_, metadata) = qpe.load_key(&pub_out, None)?;
                println!("  Fingerprint: {}", metadata.fingerprint.cyan());
                println!("  Key ID: {}", metadata.key_id.cyan());
            }
        }

        Commands::EncryptFile { files, output, output_dir, public_key, password } => {
            if files.len() > 1 && output.is_some() {
                anyhow::bail!("Cannot use --output with multiple input files. Use --output-dir instead.");
            }

            let (pub_key_data, password_data) = get_encryption_credentials(&qpe, public_key, password)?;

            for input_file in files {
                if !input_file.exists() {
                    eprintln!("{} File not found: {}", "Warning:".yellow(), input_file.display());
                    continue;
                }

                let output_path = if let Some(ref dir) = output_dir {
                    fs::create_dir_all(dir)?;
                    dir.join(format!("{}.qpe", input_file.file_name().unwrap().to_string_lossy()))
                } else if let Some(ref out) = output {
                    out.clone()
                } else {
                    PathBuf::from(format!("{}.qpe", input_file.display()))
                };

                if !cli.quiet {
                    println!("Encrypting {} -> {}", input_file.display(), output_path.display());
                }

                qpe.encrypt_file(
                    &input_file,
                    &output_path,
                    pub_key_data.as_deref(),
                    password_data.as_deref(),
                    !cli.quiet,
                )?;

                if !cli.quiet {
                    println!("{} Encryption successful for {}", "✓".green(), input_file.display());
                }
            }
        }

        Commands::DecryptFile { files, output, output_dir, private_key, password, key_password } => {
            if files.len() > 1 && output.is_some() {
                anyhow::bail!("Cannot use --output with multiple input files. Use --output-dir instead.");
            }

            let (priv_key_data, password_data) = get_decryption_credentials(
                &qpe,
                private_key,
                password,
                key_password,
            )?;

            for input_file in files {
                if !input_file.exists() {
                    eprintln!("{} File not found: {}", "Warning:".yellow(), input_file.display());
                    continue;
                }

                let output_path = if let Some(ref dir) = output_dir {
                    fs::create_dir_all(dir)?;
                    let base_name = input_file.file_stem().unwrap().to_string_lossy();
                    dir.join(base_name.to_string())
                } else if let Some(ref out) = output {
                    out.clone()
                } else {
                    let base_name = input_file.file_stem().unwrap().to_string_lossy();
                    PathBuf::from(base_name.to_string())
                };

                if !cli.quiet {
                    println!("Decrypting {} -> {}", input_file.display(), output_path.display());
                }

                qpe.decrypt_file(
                    &input_file,
                    &output_path,
                    priv_key_data.as_deref(),
                    password_data.as_deref(),
                    !cli.quiet,
                )?;

                if !cli.quiet {
                    println!("{} Decryption successful for {}", "✓".green(), input_file.display());
                }
            }
        }

        Commands::EncryptFolder { folder, output, public_key, password, compression } => {
            let output_path = output.unwrap_or_else(|| PathBuf::from(format!("{}.qpe", folder.display())));
            let (pub_key_data, password_data) = get_encryption_credentials(&qpe, public_key, password)?;

            if !cli.quiet {
                println!("Encrypting folder {} -> {}", folder.display(), output_path.display());
            }

            qpe.encrypt_folder(
                &folder,
                &output_path,
                pub_key_data.as_deref(),
                password_data.as_deref(),
                Some(compression),
                !cli.quiet,
            )?;

            if !cli.quiet {
                println!("{} Encrypted folder: {} -> {}", "✓".green(), folder.display(), output_path.display());
            }
        }

        Commands::DecryptFolder { archive, output, private_key, password, key_password } => {
            let output_path = output.unwrap_or_else(|| {
                let base = archive.file_stem().unwrap().to_string_lossy();
                PathBuf::from(format!("{}_decrypted", base))
            });

            let (priv_key_data, password_data) = get_decryption_credentials(
                &qpe,
                private_key,
                password,
                key_password,
            )?;

            if !cli.quiet {
                println!("Decrypting folder archive {} -> {}", archive.display(), output_path.display());
            }

            qpe.decrypt_folder(
                &archive,
                &output_path,
                priv_key_data.as_deref(),
                password_data.as_deref(),
                !cli.quiet,
            )?;

            if !cli.quiet {
                println!("{} Decrypted folder archive to: {}", "✓".green(), output_path.display());
            }
        }

        Commands::EncryptText { text, public_key, password } => {
            let (pub_key_data, password_data) = get_encryption_credentials(&qpe, public_key, password)?;
            
            let encrypted = qpe.encrypt_text(&text, pub_key_data.as_deref(), password_data.as_deref())?;
            
            println!("\n-----BEGIN ENCRYPTED MESSAGE-----");
            println!("{}", encrypted);
            println!("-----END ENCRYPTED MESSAGE-----");
        }

        Commands::DecryptText { text, private_key, password, key_password } => {
            let (priv_key_data, password_data) = get_decryption_credentials(
                &qpe,
                private_key,
                password,
                key_password,
            )?;

            let decrypted = qpe.decrypt_text(&text.trim(), priv_key_data.as_deref(), password_data.as_deref())?;
            
            println!("\nDecrypted Text:");
            println!("{}", decrypted);
        }

        Commands::KeyInfo { key_file } => {
            let (_, metadata) = qpe.load_key(&key_file, None)?;
            println!("{}", serde_json::to_string_pretty(&metadata)?);
        }

        Commands::GeneratePassword { length, no_symbols, readable } => {
            let password = generate_secure_password(length, !no_symbols, readable);
            println!("Generated password: {}", password.bright_green());
        }
    }

    Ok(())
}

/// Get encryption credentials (either public key or password)
fn get_encryption_credentials(
    qpe: &QuantumProofEncryption,
    public_key: Option<PathBuf>,
    use_password: bool,
) -> Result<(Option<Vec<u8>>, Option<String>)> {
    if public_key.is_some() && use_password {
        anyhow::bail!("Cannot use both --public-key and --password");
    }

    if let Some(key_path) = public_key {
        let (key, _) = qpe.load_key(&key_path, None)?;
        Ok((Some(key), None))
    } else if use_password {
        let password = prompt_password("Enter encryption password: ")?;
        Ok((None, Some(password)))
    } else {
        anyhow::bail!("Either --public-key or --password must be specified");
    }
}

/// Get decryption credentials (either private key or password)
fn get_decryption_credentials(
    qpe: &QuantumProofEncryption,
    private_key: Option<PathBuf>,
    use_password: bool,
    key_password: bool,
) -> Result<(Option<Vec<u8>>, Option<String>)> {
    if private_key.is_some() && use_password {
        anyhow::bail!("Cannot use both --private-key and --password");
    }

    if let Some(key_path) = private_key {
        let password = if key_password {
            Some(prompt_password("Enter private key password: ")?)
        } else {
            None
        };
        let (key, _) = qpe.load_key(&key_path, password.as_deref())?;
        Ok((Some(key), None))
    } else if use_password {
        let password = prompt_password("Enter decryption password: ")?;
        Ok((None, Some(password)))
    } else {
        anyhow::bail!("Either --private-key or --password must be specified");
    }
}