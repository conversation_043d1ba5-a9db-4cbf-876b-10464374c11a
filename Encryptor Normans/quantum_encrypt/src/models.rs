use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum SecurityLevel {
    Standard,
    High,
    Maximum,
}

impl SecurityLevel {
    pub fn from_str(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "standard" => Ok(SecurityLevel::Standard),
            "high" => Ok(SecurityLevel::High),
            "maximum" => Ok(SecurityLevel::Maximum),
            _ => Err(format!("Invalid security level: {}", s)),
        }
    }
    
    pub fn as_str(&self) -> &'static str {
        match self {
            SecurityLevel::Standard => "standard",
            SecurityLevel::High => "high",
            SecurityLevel::Maximum => "maximum",
        }
    }
    
    pub fn kyber_variant(&self) -> KyberVariant {
        match self {
            SecurityLevel::Standard => KyberVariant::Kyber512,
            SecurityLevel::High => KyberVariant::Kyber768,
            SecurityLevel::Maximum => KyberVariant::Kyber1024,
        }
    }
    
    pub fn nist_level(&self) -> u8 {
        match self {
            SecurityLevel::Standard => 1,
            SecurityLevel::High => 3,
            SecurityLevel::Maximum => 5,
        }
    }
    
    pub fn argon2_params(&self) -> Argon2Params {
        match self {
            SecurityLevel::Standard => Argon2Params {
                time_cost: 4,
                memory_cost: 65536,
                parallelism: 4,
            },
            SecurityLevel::High => Argon2Params {
                time_cost: 6,
                memory_cost: 131072,
                parallelism: 4,
            },
            SecurityLevel::Maximum => Argon2Params {
                time_cost: 8,
                memory_cost: 262144,
                parallelism: 4,
            },
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum KyberVariant {
    Kyber512,
    Kyber768,
    Kyber1024,
}

impl KyberVariant {
    pub fn name(&self) -> &'static str {
        match self {
            KyberVariant::Kyber512 => "Kyber512",
            KyberVariant::Kyber768 => "Kyber768",
            KyberVariant::Kyber1024 => "Kyber1024",
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Argon2Params {
    pub time_cost: u32,
    pub memory_cost: u32,
    pub parallelism: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyMetadata {
    pub format_version: String,
    pub algorithm: String,
    pub variant: KyberVariant,
    pub security_level: String,
    pub nist_level: u8,
    pub created: DateTime<Utc>,
    pub key_id: String,
    pub fingerprint: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub encrypted: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PublicKeyFile {
    pub metadata: KeyMetadata,
    pub key: String,
    pub integrity: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PrivateKeyFile {
    pub metadata: KeyMetadata,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub key: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub encrypted_key: Option<EncryptedPrivateKey>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub integrity: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub warning: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EncryptedPrivateKey {
    pub algorithm: String,
    pub kdf_params: KdfParams,
    pub iv: String,
    pub ciphertext: String,
    pub tag: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct KdfParams {
    pub time_cost: u32,
    pub memory_cost: u32,
    pub parallelism: u32,
    pub salt: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileMetadata {
    pub original_name: String,
    pub original_size: u64,
    pub modified: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub permissions: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StreamHeader {
    pub format: String,
    pub version: String,
    pub security_level: String,
    pub timestamp: DateTime<Utc>,
    pub file_metadata: FileMetadata,
    pub iv: String,
    pub algorithm: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub kyber_variant: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub kem_ciphertext: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub kdf_params: Option<KdfParams>,
}

#[derive(Zeroize, ZeroizeOnDrop)]
pub struct SecureBytes(pub Vec<u8>);

impl SecureBytes {
    pub fn new(data: Vec<u8>) -> Self {
        Self(data)
    }
    
    pub fn as_slice(&self) -> &[u8] {
        &self.0
    }
    
    pub fn len(&self) -> usize {
        self.0.len()
    }
    
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }
}

impl From<Vec<u8>> for SecureBytes {
    fn from(data: Vec<u8>) -> Self {
        Self::new(data)
    }
}

pub const FORMAT_VERSION: &str = "4.0";
pub const KEY_FORMAT_VERSION: &str = "3.0";
pub const CHUNK_SIZE: usize = 1024 * 1024; // 1 MB
pub const HEADER_LEN_BYTES: usize = 8;
pub const TAG_SIZE: usize = 16;
pub const AES_KEY_SIZE: usize = 32;
pub const IV_SIZE: usize = 12;
pub const SALT_SIZE: usize = 32;