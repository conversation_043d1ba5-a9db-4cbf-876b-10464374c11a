use crate::{QuantumProofEncryption, SecurityLevel, generate_secure_password};
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use ratatui::{
    backend::{Backend, CrosstermBackend},
    layout::{Alignment, Constraint, Direction, Layout, Margin, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{
        Block, BorderType, Borders, Clear, List, ListItem, Padding, Paragraph,
        Scrollbar, ScrollbarOrientation, ScrollbarState, Wrap,
    },
    Frame, Terminal,
};
use std::{
    error::Error,
    io,
    path::{Path, PathBuf},
    time::Duration,
};
use tui_textarea::TextArea;

#[derive(Debug, Clone, PartialEq)]
enum AppState {
    MainMenu,
    GenerateKeys,
    EncryptFile,
    DecryptFile,
    EncryptFolder,
    DecryptFolder,
    EncryptText,
    DecryptText,
    KeyInfo,
    GeneratePassword,

    Success(String),
    Error(String),
}

#[derive(Debug, Clone, PartialEq)]
enum InputField {
    None,
    PublicKeyPath,
    PrivateKeyPath,
    InputPath,
    OutputPath,
    Password,
    Text,
    KeyPassword,
}

pub struct App {
    state: AppState,
    security_level: SecurityLevel,
    qpe: QuantumProofEncryption,
    selected_menu_item: usize,
    input_field: InputField,
    
    // Input buffers
    public_key_path: String,
    private_key_path: String,
    input_path: String,
    output_path: String,
    password: String,
    key_password: String,
    text_input: TextArea<'static>,
    text_output: String,
    
    // Options
    encrypt_private_key: bool,
    use_password: bool,
    compression_level: i32,
    password_length: usize,
    no_symbols: bool,
    readable: bool,
    
    // UI State
    scroll_state: ScrollbarState,
}

impl App {
    pub fn new() -> Self {
        let security_level = SecurityLevel::High;
        let qpe = QuantumProofEncryption::new(security_level);
        
        let mut text_input = TextArea::default();
        text_input.set_block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Cyan))
                .title("Text Input"),
        );
        
        Self {
            state: AppState::MainMenu,
            security_level,
            qpe,
            selected_menu_item: 0,
            input_field: InputField::None,
            
            public_key_path: String::new(),
            private_key_path: String::new(),
            input_path: String::new(),
            output_path: String::new(),
            password: String::new(),
            key_password: String::new(),
            text_input,
            text_output: String::new(),
            
            encrypt_private_key: false,
            use_password: false,
            compression_level: 6,
            password_length: 32,
            no_symbols: false,
            readable: false,
            
            scroll_state: ScrollbarState::default(),
        }
    }

    pub fn run_tui(&mut self) -> Result<(), Box<dyn Error>> {
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
        let backend = CrosstermBackend::new(stdout);
        let mut terminal = Terminal::new(backend)?;

        let res = self.run_app(&mut terminal);

        disable_raw_mode()?;
        execute!(
            terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        )?;
        terminal.show_cursor()?;

        if let Err(err) = res {
            println!("{err:?}");
        }

        Ok(())
    }

    fn run_app<B: Backend>(&mut self, terminal: &mut Terminal<B>) -> io::Result<()> {
        loop {
            terminal.draw(|f| self.ui(f))?;

            if event::poll(Duration::from_millis(100))? {
                if let Event::Key(key) = event::read()? {
                    if key.kind == KeyEventKind::Press {
                        match self.handle_input(key.code) {
                            Ok(should_quit) => {
                                if should_quit {
                                    return Ok(());
                                }
                            }
                            Err(e) => {
                                self.state = AppState::Error(e.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    fn ui(&mut self, f: &mut Frame) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),  // Header
                Constraint::Min(0),     // Main content
                Constraint::Length(3),  // Status bar
            ])
            .split(f.area());

        // Header
        self.render_header(f, chunks[0]);

        // Main content
        match &self.state {
            AppState::MainMenu => self.render_main_menu(f, chunks[1]),
            AppState::GenerateKeys => self.render_generate_keys(f, chunks[1]),
            AppState::EncryptFile => self.render_encrypt_file(f, chunks[1]),
            AppState::DecryptFile => self.render_decrypt_file(f, chunks[1]),
            AppState::EncryptFolder => self.render_encrypt_folder(f, chunks[1]),
            AppState::DecryptFolder => self.render_decrypt_folder(f, chunks[1]),
            AppState::EncryptText => self.render_encrypt_text(f, chunks[1]),
            AppState::DecryptText => self.render_decrypt_text(f, chunks[1]),
            AppState::KeyInfo => self.render_key_info(f, chunks[1]),
            AppState::GeneratePassword => self.render_generate_password(f, chunks[1]),

            AppState::Success(msg) => self.render_success(f, chunks[1], msg),
            AppState::Error(msg) => self.render_error(f, chunks[1], msg),
        }

        // Status bar
        self.render_status_bar(f, chunks[2]);
    }

    fn render_header(&self, f: &mut Frame, area: Rect) {
        let header = Paragraph::new("🔐 Normans Quantum-Proof Encryption v4.0")
            .style(
                Style::default()
                    .fg(Color::Cyan)
                    .add_modifier(Modifier::BOLD),
            )
            .alignment(Alignment::Center)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_type(BorderType::Rounded)
                    .border_style(Style::default().fg(Color::Cyan)),
            );
        f.render_widget(header, area);
    }

    fn render_main_menu(&mut self, f: &mut Frame, area: Rect) {
        let menu_items = vec![
            ("🔑", "Generate Keys", "Create a new quantum-safe keypair"),
            ("🔒", "Encrypt File", "Encrypt a single file"),
            ("🔓", "Decrypt File", "Decrypt an encrypted file"),
            ("📁", "Encrypt Folder", "Encrypt an entire folder"),
            ("📂", "Decrypt Folder", "Decrypt a folder archive"),
            ("📝", "Encrypt Text", "Encrypt a text message"),
            ("📖", "Decrypt Text", "Decrypt a text message"),
            ("ℹ️", "Key Info", "Display key information"),
            ("🎲", "Generate Password", "Generate a secure password"),
            ("❌", "Exit", "Quit the application"),
        ];

        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Min(0), Constraint::Length(5)])
            .margin(2)
            .split(area);

        let items: Vec<ListItem> = menu_items
            .iter()
            .enumerate()
            .map(|(i, (icon, title, desc))| {
                let style = if i == self.selected_menu_item {
                    Style::default()
                        .bg(Color::Rgb(61, 39, 115))
                        .fg(Color::White)
                        .add_modifier(Modifier::BOLD)
                } else {
                    Style::default().fg(Color::Gray)
                };

                let content = vec![
                    Line::from(vec![
                        Span::raw(format!("{} ", icon)),
                        Span::styled(*title, style),
                    ]),
                    Line::from(Span::styled(
                        format!("   {}", desc),
                        Style::default().fg(Color::DarkGray),
                    )),
                ];
                ListItem::new(content)
            })
            .collect();

        let menu = List::new(items)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_type(BorderType::Rounded)
                    .title("Main Menu")
                    .title_alignment(Alignment::Center)
                    .padding(Padding::horizontal(2)),
            )
            .style(Style::default().fg(Color::White));

        f.render_widget(menu, chunks[0]);

        // Security level selector
        let security_info = Paragraph::new(vec![
            Line::from(vec![
                Span::raw("Security Level: "),
                Span::styled(
                    self.security_level.as_str().to_uppercase(),
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD),
                ),
            ]),
            Line::from(Span::raw("Press [S] to cycle security levels")),
        ])
        .block(
            Block::default()
                .borders(Borders::ALL)
                .border_type(BorderType::Rounded)
                .title("Settings"),
        )
        .alignment(Alignment::Center);

        f.render_widget(security_info, chunks[1]);
    }

    fn render_generate_keys(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Generate Quantum-Safe Keypair");

        f.render_widget(block, area);

        // Public key path
        let pub_input = self.render_input_field(
            "Public Key Path",
            &self.public_key_path,
            self.input_field == InputField::PublicKeyPath,
            "public.key",
        );
        f.render_widget(pub_input, chunks[0]);

        // Private key path
        let priv_input = self.render_input_field(
            "Private Key Path",
            &self.private_key_path,
            self.input_field == InputField::PrivateKeyPath,
            "private.key",
        );
        f.render_widget(priv_input, chunks[1]);

        // Options
        let options = Paragraph::new(vec![
            Line::from(vec![
                Span::raw("["),
                Span::styled(
                    if self.encrypt_private_key { "✓" } else { " " },
                    Style::default().fg(Color::Green),
                ),
                Span::raw("] Encrypt private key with password (Press [E])"),
            ]),
        ])
        .block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Gray)),
        );
        f.render_widget(options, chunks[2]);

        // Instructions
        let help = self.render_help_text(vec![
            "[TAB] Switch fields",
            "[E] Toggle key encryption",
            "[Enter] Generate keypair",
            "[Esc] Back to menu",
        ]);
        f.render_widget(help, chunks[3]);
    }

    fn render_encrypt_file(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Encrypt File");

        f.render_widget(block, area);

        // Input file
        let input = self.render_input_field(
            "File to Encrypt",
            &self.input_path,
            self.input_field == InputField::InputPath,
            "document.pdf",
        );
        f.render_widget(input, chunks[0]);

        // Output file
        let output = self.render_input_field(
            "Output File",
            &self.output_path,
            self.input_field == InputField::OutputPath,
            "document.pdf.qpe",
        );
        f.render_widget(output, chunks[1]);

        // Encryption method
        let method = if self.use_password {
            let pwd_input = self.render_password_field(
                "Encryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[2]);
            "Password"
        } else {
            let key_input = self.render_input_field(
                "Public Key File",
                &self.public_key_path,
                self.input_field == InputField::PublicKeyPath,
                "recipient.pub",
            );
            f.render_widget(key_input, chunks[2]);
            "Public Key"
        };

        // Options
        let options = Paragraph::new(vec![
            Line::from(vec![
                Span::raw("Encryption Method: "),
                Span::styled(
                    method,
                    Style::default()
                        .fg(Color::Yellow)
                        .add_modifier(Modifier::BOLD),
                ),
                Span::raw(" (Press [M] to toggle)"),
            ]),
        ])
        .block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Gray)),
        );
        f.render_widget(options, chunks[3]);

        // Help
        let help = self.render_help_text(vec![
            "[TAB] Switch fields",
            "[M] Toggle encryption method",
            "[Enter] Encrypt file",
            "[Esc] Back to menu",
        ]);
        f.render_widget(help, chunks[4]);
    }

    fn render_decrypt_file(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Decrypt File");

        f.render_widget(block, area);

        // Input file
        let input = self.render_input_field(
            "Encrypted File",
            &self.input_path,
            self.input_field == InputField::InputPath,
            "document.pdf.qpe",
        );
        f.render_widget(input, chunks[0]);

        // Output file
        let output = self.render_input_field(
            "Output File",
            &self.output_path,
            self.input_field == InputField::OutputPath,
            "document.pdf",
        );
        f.render_widget(output, chunks[1]);

        // Decryption method
        if self.use_password {
            let pwd_input = self.render_password_field(
                "Decryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[2]);
        } else {
            let key_input = self.render_input_field(
                "Private Key File",
                &self.private_key_path,
                self.input_field == InputField::PrivateKeyPath,
                "private.key",
            );
            f.render_widget(key_input, chunks[2]);

            if !self.private_key_path.is_empty() {
                let key_pwd = self.render_password_field(
                    "Key Password (if encrypted)",
                    &self.key_password,
                    self.input_field == InputField::KeyPassword,
                );
                f.render_widget(key_pwd, chunks[3]);
            }
        }

        // Help
        let help = self.render_help_text(vec![
            "[TAB] Switch fields",
            "[M] Toggle decryption method",
            "[Enter] Decrypt file",
            "[Esc] Back to menu",
        ]);
        f.render_widget(help, chunks[4]);
    }

    fn render_encrypt_text(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(10),
                Constraint::Length(3),
                Constraint::Length(10),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Encrypt Text Message");

        f.render_widget(block, area);

        // Text input area
        if self.input_field == InputField::Text {
            self.text_input.set_cursor_line_style(Style::default().bg(Color::Rgb(50, 50, 50)));
            f.render_widget(&self.text_input, chunks[0]);
        } else {
            let text_block = Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::DarkGray))
                .title("Text Input (Click or TAB to edit)");
            let text = Paragraph::new(self.text_input.lines().join("\n"))
                .block(text_block)
                .wrap(Wrap { trim: false });
            f.render_widget(text, chunks[0]);
        }

        // Encryption options
        if self.use_password {
            let pwd_input = self.render_password_field(
                "Encryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[1]);
        } else {
            let key_input = self.render_input_field(
                "Public Key File",
                &self.public_key_path,
                self.input_field == InputField::PublicKeyPath,
                "recipient.pub",
            );
            f.render_widget(key_input, chunks[1]);
        }

        // Output
        if !self.text_output.is_empty() {
            let output = Paragraph::new(self.text_output.clone())
                .block(
                    Block::default()
                        .borders(Borders::ALL)
                        .border_type(BorderType::Double)
                        .border_style(Style::default().fg(Color::Green))
                        .title("Encrypted Output"),
                )
                .wrap(Wrap { trim: true })
                .style(Style::default().fg(Color::Green));
            f.render_widget(output, chunks[2]);
        }
    }



    fn render_success(&self, f: &mut Frame, area: Rect, message: &str) {
        let popup_area = self.centered_rect(60, 20, area);
        f.render_widget(Clear, popup_area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Thick)
            .border_style(Style::default().fg(Color::Green))
            .title("Success")
            .title_alignment(Alignment::Center);

        let text = vec![
            Line::from(""),
            Line::from(Span::styled(
                "✓",
                Style::default()
                    .fg(Color::Green)
                    .add_modifier(Modifier::BOLD),
            )),
            Line::from(""),
            Line::from(Span::raw(message)),
            Line::from(""),
            Line::from(Span::styled(
                "Press any key to continue",
                Style::default().fg(Color::Gray),
            )),
        ];

        let paragraph = Paragraph::new(text)
            .block(block)
            .alignment(Alignment::Center)
            .wrap(Wrap { trim: true });

        f.render_widget(paragraph, popup_area);
    }

    fn render_error(&self, f: &mut Frame, area: Rect, message: &str) {
        let popup_area = self.centered_rect(60, 20, area);
        f.render_widget(Clear, popup_area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Thick)
            .border_style(Style::default().fg(Color::Red))
            .title("Error")
            .title_alignment(Alignment::Center);

        let text = vec![
            Line::from(""),
            Line::from(Span::styled(
                "✗",
                Style::default()
                    .fg(Color::Red)
                    .add_modifier(Modifier::BOLD),
            )),
            Line::from(""),
            Line::from(Span::raw(message)),
            Line::from(""),
            Line::from(Span::styled(
                "Press any key to continue",
                Style::default().fg(Color::Gray),
            )),
        ];

        let paragraph = Paragraph::new(text)
            .block(block)
            .alignment(Alignment::Center)
            .wrap(Wrap { trim: true });

        f.render_widget(paragraph, popup_area);
    }

    fn render_status_bar(&self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage(33),
                Constraint::Percentage(34),
                Constraint::Percentage(33),
            ])
            .split(area);

        // Left: Security level
        let security = Paragraph::new(format!(
            "🛡️  Security: {}",
            self.security_level.as_str().to_uppercase()
        ))
        .style(Style::default().fg(Color::Yellow));
        f.render_widget(security, chunks[0]);

        // Center: Current state
        let state_text = match &self.state {
            AppState::MainMenu => "Main Menu",
            AppState::GenerateKeys => "Generate Keys",
            AppState::EncryptFile => "Encrypt File",
            AppState::DecryptFile => "Decrypt File",
            AppState::EncryptFolder => "Encrypt Folder",
            AppState::DecryptFolder => "Decrypt Folder",
            AppState::EncryptText => "Encrypt Text",
            AppState::DecryptText => "Decrypt Text",
            AppState::KeyInfo => "Key Information",
            AppState::GeneratePassword => "Generate Password",

            AppState::Success(_) => "Success",
            AppState::Error(_) => "Error",
        };
        let state = Paragraph::new(state_text)
            .style(Style::default().fg(Color::Cyan))
            .alignment(Alignment::Center);
        f.render_widget(state, chunks[1]);

        // Right: Help
        let help = Paragraph::new("📚 [F1] Help | [Q] Quit")
            .style(Style::default().fg(Color::Gray))
            .alignment(Alignment::Right);
        f.render_widget(help, chunks[2]);
    }

    fn render_input_field(
        &self,
        label: &str,
        value: &str,
        is_active: bool,
        placeholder: &str,
    ) -> Paragraph {
        let style = if is_active {
            Style::default().fg(Color::Yellow)
        } else {
            Style::default().fg(Color::Gray)
        };

        let display_value = if value.is_empty() && !is_active {
            placeholder.to_string()
        } else {
            value.to_string()
        };

        Paragraph::new(display_value)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_style(style)
                    .title(label.to_string()),
            )
            .style(style)
    }

    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {
        let style = if is_active {
            Style::default().fg(Color::Yellow)
        } else {
            Style::default().fg(Color::Gray)
        };

        let display_value = if value.is_empty() {
            String::new()
        } else {
            "•".repeat(value.len())
        };

        Paragraph::new(display_value)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_style(style)
                    .title(label.to_string()),
            )
            .style(style)
    }

    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {
        let text: Vec<Line> = items
            .iter()
            .map(|item| Line::from(Span::raw(item.to_string())))
            .collect();

        Paragraph::new(text)
            .block(
                Block::default()
                    .borders(Borders::TOP)
                    .border_style(Style::default().fg(Color::DarkGray))
                    .title("Help"),
            )
            .style(Style::default().fg(Color::Gray))
    }

    fn centered_rect(&self, percent_x: u16, percent_y: u16, r: Rect) -> Rect {
        let popup_layout = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Percentage((100 - percent_y) / 2),
                Constraint::Percentage(percent_y),
                Constraint::Percentage((100 - percent_y) / 2),
            ])
            .split(r);

        Layout::default()
            .direction(Direction::Horizontal)
            .constraints([
                Constraint::Percentage((100 - percent_x) / 2),
                Constraint::Percentage(percent_x),
                Constraint::Percentage((100 - percent_x) / 2),
            ])
            .split(popup_layout[1])[1]
    }

    fn render_generate_password(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(5),
                Constraint::Length(3),
                Constraint::Min(5),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Generate Secure Password");

        f.render_widget(block, area);

        // Options
        let options = vec![
            Line::from(vec![
                Span::raw(format!("Length: ")),
                Span::styled(
                    format!("{}", self.password_length),
                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD),
                ),
                Span::raw(" (Use ← → to adjust)"),
            ]),
            Line::from(vec![
                Span::raw("["),
                Span::styled(
                    if !self.no_symbols { "✓" } else { " " },
                    Style::default().fg(Color::Green),
                ),
                Span::raw("] Include symbols (Press [S])"),
            ]),
            Line::from(vec![
                Span::raw("["),
                Span::styled(
                    if self.readable { "✓" } else { " " },
                    Style::default().fg(Color::Green),
                ),
                Span::raw("] Human-readable (Press [R])"),
            ]),
        ];

        let options_widget = Paragraph::new(options)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Gray)),
            );
        f.render_widget(options_widget, chunks[0]);

        // Generate button
        let generate_btn = Paragraph::new("Press [G] to Generate Password")
            .style(
                Style::default()
                    .fg(Color::Black)
                    .bg(Color::Cyan)
                    .add_modifier(Modifier::BOLD),
            )
            .alignment(Alignment::Center)
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .border_style(Style::default().fg(Color::Cyan)),
            );
        f.render_widget(generate_btn, chunks[1]);

        // Generated password display
        if !self.text_output.is_empty() {
            let password_display = Paragraph::new(self.text_output.clone())
                .block(
                    Block::default()
                        .borders(Borders::ALL)
                        .border_type(BorderType::Double)
                        .border_style(Style::default().fg(Color::Green))
                        .title("Generated Password"),
                )
                .style(
                    Style::default()
                        .fg(Color::Green)
                        .add_modifier(Modifier::BOLD),
                )
                .alignment(Alignment::Center);
            f.render_widget(password_display, chunks[2]);
        }
    }

    fn render_key_info(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Key Information");

        f.render_widget(block, area);

        // Key file input
        let input = self.render_input_field(
            "Key File Path",
            &self.input_path,
            self.input_field == InputField::InputPath,
            "public.key or private.key",
        );
        f.render_widget(input, chunks[0]);

        // Display key info if available
        if !self.text_output.is_empty() {
            let info = Paragraph::new(self.text_output.clone())
                .block(
                    Block::default()
                        .borders(Borders::ALL)
                        .border_style(Style::default().fg(Color::Cyan))
                        .title("Key Details"),
                )
                .wrap(Wrap { trim: false })
                .scroll((0, 0));
            f.render_widget(info, chunks[1]);

            // Scrollbar
            let scrollbar = Scrollbar::default()
                .orientation(ScrollbarOrientation::VerticalRight)
                .begin_symbol(Some("↑"))
                .end_symbol(Some("↓"));
            f.render_stateful_widget(
                scrollbar,
                chunks[1].inner(Margin { vertical: 1, horizontal: 0 }),
                &mut self.scroll_state,
            );
        }
    }

    fn render_encrypt_folder(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Encrypt Folder");

        f.render_widget(block, area);

        // Input folder
        let input = self.render_input_field(
            "Folder to Encrypt",
            &self.input_path,
            self.input_field == InputField::InputPath,
            "/path/to/folder",
        );
        f.render_widget(input, chunks[0]);

        // Output file
        let output = self.render_input_field(
            "Output Archive",
            &self.output_path,
            self.input_field == InputField::OutputPath,
            "folder.qpe",
        );
        f.render_widget(output, chunks[1]);

        // Encryption method
        if self.use_password {
            let pwd_input = self.render_password_field(
                "Encryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[2]);
        } else {
            let key_input = self.render_input_field(
                "Public Key File",
                &self.public_key_path,
                self.input_field == InputField::PublicKeyPath,
                "recipient.pub",
            );
            f.render_widget(key_input, chunks[2]);
        }

        // Compression level
        let compression = Paragraph::new(format!(
            "Compression Level: {} (Use ← → to adjust)",
            self.compression_level
        ))
        .block(
            Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::Gray)),
        );
        f.render_widget(compression, chunks[3]);

        // Help
        let help = self.render_help_text(vec![
            "[TAB] Switch fields",
            "[M] Toggle encryption method",
            "[← →] Adjust compression",
            "[Enter] Encrypt folder",
            "[Esc] Back to menu",
        ]);
        f.render_widget(help, chunks[4]);
    }

    fn render_decrypt_folder(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Length(3),
                Constraint::Min(0),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Decrypt Folder Archive");

        f.render_widget(block, area);

        // Input archive
        let input = self.render_input_field(
            "Encrypted Archive",
            &self.input_path,
            self.input_field == InputField::InputPath,
            "folder.qpe",
        );
        f.render_widget(input, chunks[0]);

        // Output folder
        let output = self.render_input_field(
            "Output Folder",
            &self.output_path,
            self.input_field == InputField::OutputPath,
            "/path/to/output",
        );
        f.render_widget(output, chunks[1]);

        // Decryption method
        if self.use_password {
            let pwd_input = self.render_password_field(
                "Decryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[2]);
        } else {
            let key_input = self.render_input_field(
                "Private Key File",
                &self.private_key_path,
                self.input_field == InputField::PrivateKeyPath,
                "private.key",
            );
            f.render_widget(key_input, chunks[2]);

            if !self.private_key_path.is_empty() {
                let key_pwd = self.render_password_field(
                    "Key Password (if encrypted)",
                    &self.key_password,
                    self.input_field == InputField::KeyPassword,
                );
                f.render_widget(key_pwd, chunks[3]);
            }
        }

        // Help
        let help = self.render_help_text(vec![
            "[TAB] Switch fields",
            "[M] Toggle decryption method",
            "[Enter] Decrypt archive",
            "[Esc] Back to menu",
        ]);
        f.render_widget(help, chunks[4]);
    }

    fn render_decrypt_text(&mut self, f: &mut Frame, area: Rect) {
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([
                Constraint::Min(8),
                Constraint::Length(3),
                Constraint::Min(8),
            ])
            .margin(2)
            .split(area);

        let block = Block::default()
            .borders(Borders::ALL)
            .border_type(BorderType::Rounded)
            .title("Decrypt Text Message");

        f.render_widget(block, area);

        // Encrypted text input
        if self.input_field == InputField::Text {
            self.text_input.set_cursor_line_style(Style::default().bg(Color::Rgb(50, 50, 50)));
            f.render_widget(&self.text_input, chunks[0]);
        } else {
            let text_block = Block::default()
                .borders(Borders::ALL)
                .border_style(Style::default().fg(Color::DarkGray))
                .title("Encrypted Text (Click or TAB to edit)");
            let text = Paragraph::new(self.text_input.lines().join("\n"))
                .block(text_block)
                .wrap(Wrap { trim: false });
            f.render_widget(text, chunks[0]);
        }

        // Decryption options
        if self.use_password {
            let pwd_input = self.render_password_field(
                "Decryption Password",
                &self.password,
                self.input_field == InputField::Password,
            );
            f.render_widget(pwd_input, chunks[1]);
        } else {
            let key_input = self.render_input_field(
                "Private Key File",
                &self.private_key_path,
                self.input_field == InputField::PrivateKeyPath,
                "private.key",
            );
            f.render_widget(key_input, chunks[1]);
        }

        // Decrypted output
        if !self.text_output.is_empty() {
            let output = Paragraph::new(self.text_output.clone())
                .block(
                    Block::default()
                        .borders(Borders::ALL)
                        .border_type(BorderType::Double)
                        .border_style(Style::default().fg(Color::Green))
                        .title("Decrypted Text"),
                )
                .wrap(Wrap { trim: true })
                .style(Style::default().fg(Color::White));
            f.render_widget(output, chunks[2]);
        }
    }

    fn handle_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        // Handle text area input separately
        if self.input_field == InputField::Text {
            match key {
                KeyCode::Esc => {
                    self.input_field = InputField::None;
                    return Ok(false);
                }
                KeyCode::Tab => {
                    self.input_field = InputField::Password;
                    return Ok(false);
                }
                _ => {
                    // For now, just skip text input for compatibility
                    // self.text_input.input(Input::from(crossterm::event::Event::Key(key)));
                    return Ok(false);
                }
            }
        }

        // Global keys
        match key {
            KeyCode::Char('q') | KeyCode::Char('Q') => return Ok(true),
            KeyCode::F(1) => {
                self.show_help();
                return Ok(false);
            }
            _ => {}
        }

        // State-specific handling
        match &self.state {
            AppState::MainMenu => self.handle_main_menu_input(key),
            AppState::GenerateKeys => self.handle_generate_keys_input(key),
            AppState::EncryptFile => self.handle_encrypt_file_input(key),
            AppState::DecryptFile => self.handle_decrypt_file_input(key),
            AppState::EncryptFolder => self.handle_encrypt_folder_input(key),
            AppState::DecryptFolder => self.handle_decrypt_folder_input(key),
            AppState::EncryptText => self.handle_encrypt_text_input(key),
            AppState::DecryptText => self.handle_decrypt_text_input(key),
            AppState::KeyInfo => self.handle_key_info_input(key),
            AppState::GeneratePassword => self.handle_generate_password_input(key),
            AppState::Success(_) | AppState::Error(_) => {
                self.state = AppState::MainMenu;
                Ok(false)
            }

        }
    }

    fn handle_main_menu_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Up => {
                if self.selected_menu_item > 0 {
                    self.selected_menu_item -= 1;
                }
            }
            KeyCode::Down => {
                if self.selected_menu_item < 9 {
                    self.selected_menu_item += 1;
                }
            }
            KeyCode::Char('s') | KeyCode::Char('S') => {
                // Cycle security levels
                self.security_level = match self.security_level {
                    SecurityLevel::Standard => SecurityLevel::High,
                    SecurityLevel::High => SecurityLevel::Maximum,
                    SecurityLevel::Maximum => SecurityLevel::Standard,
                };
                self.qpe = QuantumProofEncryption::new(self.security_level);
            }
            KeyCode::Enter => {
                match self.selected_menu_item {
                    0 => {
                        self.state = AppState::GenerateKeys;
                        self.input_field = InputField::PublicKeyPath;
                        self.public_key_path = "public.key".to_string();
                        self.private_key_path = "private.key".to_string();
                    }
                    1 => {
                        self.state = AppState::EncryptFile;
                        self.input_field = InputField::InputPath;
                    }
                    2 => {
                        self.state = AppState::DecryptFile;
                        self.input_field = InputField::InputPath;
                    }
                    3 => {
                        self.state = AppState::EncryptFolder;
                        self.input_field = InputField::InputPath;
                    }
                    4 => {
                        self.state = AppState::DecryptFolder;
                        self.input_field = InputField::InputPath;
                    }
                    5 => {
                        self.state = AppState::EncryptText;
                        self.input_field = InputField::Text;
                        self.text_input.select_all();
                    }
                    6 => {
                        self.state = AppState::DecryptText;
                        self.input_field = InputField::Text;
                        self.text_input.select_all();
                    }
                    7 => {
                        self.state = AppState::KeyInfo;
                        self.input_field = InputField::InputPath;
                    }
                    8 => {
                        self.state = AppState::GeneratePassword;
                        self.text_output.clear();
                    }
                    9 => return Ok(true), // Exit
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_generate_keys_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
            }
            KeyCode::Tab => {
                self.input_field = match self.input_field {
                    InputField::PublicKeyPath => InputField::PrivateKeyPath,
                    _ => InputField::PublicKeyPath,
                };
            }
            KeyCode::Char('e') | KeyCode::Char('E') => {
                self.encrypt_private_key = !self.encrypt_private_key;
            }
            KeyCode::Enter => {
                let pub_path = if self.public_key_path.is_empty() {
                    "public.key"
                } else {
                    &self.public_key_path
                };
                let priv_path = if self.private_key_path.is_empty() {
                    "private.key"
                } else {
                    &self.private_key_path
                };

                match self.qpe.generate_keypair() {
                    Ok((pub_key, priv_key)) => {
                        let key_password = if self.encrypt_private_key {
                            // In a real app, we'd prompt for password
                            Some("password123") // Placeholder
                        } else {
                            None
                        };

                        match self.qpe.save_keypair(
                            &pub_key,
                            &priv_key,
                            Path::new(pub_path),
                            Path::new(priv_path),
                            key_password,
                        ) {
                            Ok(_) => {
                                let msg = format!(
                                    "Keypair generated successfully!\nPublic: {}\nPrivate: {}",
                                    pub_path, priv_path
                                );
                                self.state = AppState::Success(msg);
                            }
                            Err(e) => {
                                self.state = AppState::Error(format!("Failed to save keys: {}", e));
                            }
                        }
                    }
                    Err(e) => {
                        self.state = AppState::Error(format!("Failed to generate keys: {}", e));
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::PublicKeyPath => { self.public_key_path.pop(); }
                    InputField::PrivateKeyPath => { self.private_key_path.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::PublicKeyPath => self.public_key_path.push(c),
                    InputField::PrivateKeyPath => self.private_key_path.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_encrypt_file_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
            }
            KeyCode::Tab => {
                self.input_field = match self.input_field {
                    InputField::InputPath => InputField::OutputPath,
                    InputField::OutputPath => {
                        if self.use_password {
                            InputField::Password
                        } else {
                            InputField::PublicKeyPath
                        }
                    }
                    _ => InputField::InputPath,
                };
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                self.use_password = !self.use_password;
            }
            KeyCode::Enter => {
                let input_path = Path::new(&self.input_path);
                let output_path = if self.output_path.is_empty() {
                    PathBuf::from(format!("{}.qpe", self.input_path))
                } else {
                    PathBuf::from(&self.output_path)
                };

                if !input_path.exists() {
                    self.state = AppState::Error("Input file not found".to_string());
                    return Ok(false);
                }

                let result = if self.use_password {
                    self.qpe.encrypt_file(
                        input_path,
                        &output_path,
                        None,
                        Some(&self.password),
                        false,
                    )
                } else {
                    match self.qpe.load_key(Path::new(&self.public_key_path), None) {
                        Ok((pub_key, _)) => {
                            self.qpe.encrypt_file(
                                input_path,
                                &output_path,
                                Some(&pub_key),
                                None,
                                false,
                            )
                        }
                        Err(e) => Err(e),
                    }
                };

                match result {
                    Ok(_) => {
                        self.state = AppState::Success(
                            format!("File encrypted successfully to {}", output_path.display())
                        );
                    }
                    Err(e) => {
                        self.state = AppState::Error(format!("Encryption failed: {}", e));
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::InputPath => { self.input_path.pop(); }
                    InputField::OutputPath => { self.output_path.pop(); }
                    InputField::PublicKeyPath => { self.public_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::InputPath => self.input_path.push(c),
                    InputField::OutputPath => self.output_path.push(c),
                    InputField::PublicKeyPath => self.public_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_decrypt_file_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
            }
            KeyCode::Tab => {
                self.input_field = match self.input_field {
                    InputField::InputPath => InputField::OutputPath,
                    InputField::OutputPath => {
                        if self.use_password {
                            InputField::Password
                        } else {
                            InputField::PrivateKeyPath
                        }
                    }
                    InputField::PrivateKeyPath => InputField::KeyPassword,
                    _ => InputField::InputPath,
                };
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                self.use_password = !self.use_password;
            }
            KeyCode::Enter => {
                let input_path = Path::new(&self.input_path);
                let output_path = if self.output_path.is_empty() {
                    let stem = input_path.file_stem().unwrap_or_default();
                    PathBuf::from(stem.to_string_lossy().to_string())
                } else {
                    PathBuf::from(&self.output_path)
                };

                if !input_path.exists() {
                    self.state = AppState::Error("Input file not found".to_string());
                    return Ok(false);
                }

                let result = if self.use_password {
                    self.qpe.decrypt_file(
                        input_path,
                        &output_path,
                        None,
                        Some(&self.password),
                        false,
                    )
                } else {
                    let key_pwd = if self.key_password.is_empty() {
                        None
                    } else {
                        Some(self.key_password.as_str())
                    };
                    
                    match self.qpe.load_key(Path::new(&self.private_key_path), key_pwd) {
                        Ok((priv_key, _)) => {
                            self.qpe.decrypt_file(
                                input_path,
                                &output_path,
                                Some(&priv_key),
                                None,
                                false,
                            )
                        }
                        Err(e) => Err(e),
                    }
                };

                match result {
                    Ok(_) => {
                        self.state = AppState::Success(
                            format!("File decrypted successfully to {}", output_path.display())
                        );
                    }
                    Err(e) => {
                        self.state = AppState::Error(format!("Decryption failed: {}", e));
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::InputPath => { self.input_path.pop(); }
                    InputField::OutputPath => { self.output_path.pop(); }
                    InputField::PrivateKeyPath => { self.private_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    InputField::KeyPassword => { self.key_password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::InputPath => self.input_path.push(c),
                    InputField::OutputPath => self.output_path.push(c),
                    InputField::PrivateKeyPath => self.private_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    InputField::KeyPassword => self.key_password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_encrypt_text_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                if self.input_field == InputField::Text {
                    self.input_field = InputField::None;
                } else {
                    self.state = AppState::MainMenu;
                    self.input_field = InputField::None;
                }
            }
            KeyCode::Tab => {
                if self.input_field != InputField::Text {
                    self.input_field = InputField::Text;
                } else {
                    self.input_field = if self.use_password {
                        InputField::Password
                    } else {
                        InputField::PublicKeyPath
                    };
                }
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                if self.input_field != InputField::Text {
                    self.use_password = !self.use_password;
                }
            }
            KeyCode::Enter => {
                if self.input_field == InputField::Text {
                    self.input_field = InputField::None;
                } else {
                    let text = self.text_input.lines().join("\n");
                    
                    let result = if self.use_password {
                        self.qpe.encrypt_text(&text, None, Some(&self.password))
                    } else {
                        match self.qpe.load_key(Path::new(&self.public_key_path), None) {
                            Ok((pub_key, _)) => {
                                self.qpe.encrypt_text(&text, Some(&pub_key), None)
                            }
                            Err(e) => Err(e),
                        }
                    };

                    match result {
                        Ok(encrypted) => {
                            self.text_output = encrypted;
                        }
                        Err(e) => {
                            self.state = AppState::Error(format!("Encryption failed: {}", e));
                        }
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::PublicKeyPath => { self.public_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::PublicKeyPath => self.public_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_decrypt_text_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                if self.input_field == InputField::Text {
                    self.input_field = InputField::None;
                } else {
                    self.state = AppState::MainMenu;
                    self.input_field = InputField::None;
                }
            }
            KeyCode::Tab => {
                if self.input_field != InputField::Text {
                    self.input_field = InputField::Text;
                } else {
                    self.input_field = if self.use_password {
                        InputField::Password
                    } else {
                        InputField::PrivateKeyPath
                    };
                }
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                if self.input_field != InputField::Text {
                    self.use_password = !self.use_password;
                }
            }
            KeyCode::Enter => {
                if self.input_field == InputField::Text {
                    self.input_field = InputField::None;
                } else {
                    let encrypted_text = self.text_input.lines().join("\n").trim().to_string();
                    
                    let result = if self.use_password {
                        self.qpe.decrypt_text(&encrypted_text, None, Some(&self.password))
                    } else {
                        let key_pwd = if self.key_password.is_empty() {
                            None
                        } else {
                            Some(self.key_password.as_str())
                        };
                        
                        match self.qpe.load_key(Path::new(&self.private_key_path), key_pwd) {
                            Ok((priv_key, _)) => {
                                self.qpe.decrypt_text(&encrypted_text, Some(&priv_key), None)
                            }
                            Err(e) => Err(e),
                        }
                    };

                    match result {
                        Ok(decrypted) => {
                            self.text_output = decrypted;
                        }
                        Err(e) => {
                            self.state = AppState::Error(format!("Decryption failed: {}", e));
                        }
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::PrivateKeyPath => { self.private_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    InputField::KeyPassword => { self.key_password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::PrivateKeyPath => self.private_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    InputField::KeyPassword => self.key_password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_key_info_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
                self.text_output.clear();
            }
            KeyCode::Enter => {
                match self.qpe.load_key(Path::new(&self.input_path), None) {
                    Ok((_, metadata)) => {
                        self.text_output = serde_json::to_string_pretty(&metadata).unwrap();
                        self.scroll_state = self.scroll_state.content_length(
                            self.text_output.lines().count()
                        );
                    }
                    Err(e) => {
                        self.state = AppState::Error(format!("Failed to load key: {}", e));
                    }
                }
            }
            KeyCode::Up => {
                // Scroll up functionality - simplified for compatibility
            }
            KeyCode::Down => {
                // Scroll down functionality - simplified for compatibility
            }
            KeyCode::Backspace => {
                if self.input_field == InputField::InputPath {
                    self.input_path.pop();
                }
            }
            KeyCode::Char(c) => {
                if self.input_field == InputField::InputPath {
                    self.input_path.push(c);
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_generate_password_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.text_output.clear();
            }
            KeyCode::Left => {
                if self.password_length > 8 {
                    self.password_length -= 1;
                }
            }
            KeyCode::Right => {
                if self.password_length < 128 {
                    self.password_length += 1;
                }
            }
            KeyCode::Char('s') | KeyCode::Char('S') => {
                self.no_symbols = !self.no_symbols;
            }
            KeyCode::Char('r') | KeyCode::Char('R') => {
                self.readable = !self.readable;
            }
            KeyCode::Char('g') | KeyCode::Char('G') => {
                let password = generate_secure_password(
                    self.password_length,
                    !self.no_symbols,
                    self.readable,
                );
                self.text_output = password;
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_encrypt_folder_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
            }
            KeyCode::Tab => {
                self.input_field = match self.input_field {
                    InputField::InputPath => InputField::OutputPath,
                    InputField::OutputPath => {
                        if self.use_password {
                            InputField::Password
                        } else {
                            InputField::PublicKeyPath
                        }
                    }
                    _ => InputField::InputPath,
                };
            }
            KeyCode::Left => {
                if self.compression_level > 0 {
                    self.compression_level -= 1;
                }
            }
            KeyCode::Right => {
                if self.compression_level < 9 {
                    self.compression_level += 1;
                }
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                self.use_password = !self.use_password;
            }
            KeyCode::Enter => {
                let input_path = Path::new(&self.input_path);
                let output_path = if self.output_path.is_empty() {
                    PathBuf::from(format!("{}.qpe", self.input_path))
                } else {
                    PathBuf::from(&self.output_path)
                };

                if !input_path.exists() || !input_path.is_dir() {
                    self.state = AppState::Error("Input folder not found".to_string());
                    return Ok(false);
                }

                let result = if self.use_password {
                    self.qpe.encrypt_folder(
                        input_path,
                        &output_path,
                        None,
                        Some(&self.password),
                        Some(self.compression_level),
                        false,
                    )
                } else {
                    match self.qpe.load_key(Path::new(&self.public_key_path), None) {
                        Ok((pub_key, _)) => {
                            self.qpe.encrypt_folder(
                                input_path,
                                &output_path,
                                Some(&pub_key),
                                None,
                                Some(self.compression_level),
                                false,
                            )
                        }
                        Err(e) => Err(e),
                    }
                };

                match result {
                    Ok(_) => {
                        self.state = AppState::Success(
                            format!("Folder encrypted successfully to {}", output_path.display())
                        );
                    }
                    Err(e) => {
                        self.state = AppState::Error(format!("Encryption failed: {}", e));
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::InputPath => { self.input_path.pop(); }
                    InputField::OutputPath => { self.output_path.pop(); }
                    InputField::PublicKeyPath => { self.public_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::InputPath => self.input_path.push(c),
                    InputField::OutputPath => self.output_path.push(c),
                    InputField::PublicKeyPath => self.public_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn handle_decrypt_folder_input(&mut self, key: KeyCode) -> Result<bool, Box<dyn Error>> {
        match key {
            KeyCode::Esc => {
                self.state = AppState::MainMenu;
                self.input_field = InputField::None;
            }
            KeyCode::Tab => {
                self.input_field = match self.input_field {
                    InputField::InputPath => InputField::OutputPath,
                    InputField::OutputPath => {
                        if self.use_password {
                            InputField::Password
                        } else {
                            InputField::PrivateKeyPath
                        }
                    }
                    InputField::PrivateKeyPath => InputField::KeyPassword,
                    _ => InputField::InputPath,
                };
            }
            KeyCode::Char('m') | KeyCode::Char('M') => {
                self.use_password = !self.use_password;
            }
            KeyCode::Enter => {
                let input_path = Path::new(&self.input_path);
                let output_path = if self.output_path.is_empty() {
                    let stem = input_path.file_stem().unwrap_or_default();
                    PathBuf::from(format!("{}_decrypted", stem.to_string_lossy()))
                } else {
                    PathBuf::from(&self.output_path)
                };

                // Enhanced validation
                if self.input_path.is_empty() {
                    self.state = AppState::Error("Please specify an encrypted archive file".to_string());
                    return Ok(false);
                }

                if !input_path.exists() {
                    self.state = AppState::Error(format!("Archive file not found: {}", self.input_path));
                    return Ok(false);
                }

                if !input_path.is_file() {
                    self.state = AppState::Error(format!("'{}' is not a file", self.input_path));
                    return Ok(false);
                }

                // Validate credentials
                if self.use_password {
                    if self.password.is_empty() {
                        self.state = AppState::Error("Please enter a password".to_string());
                        return Ok(false);
                    }
                } else {
                    if self.private_key_path.is_empty() {
                        self.state = AppState::Error("Please specify a private key file".to_string());
                        return Ok(false);
                    }
                    if !Path::new(&self.private_key_path).exists() {
                        self.state = AppState::Error(format!("Private key file not found: {}", self.private_key_path));
                        return Ok(false);
                    }
                }

                let result = if self.use_password {
                    self.qpe.decrypt_folder(
                        input_path,
                        &output_path,
                        None,
                        Some(&self.password),
                        true, // Show progress
                    )
                } else {
                    let key_pwd = if self.key_password.is_empty() {
                        None
                    } else {
                        Some(self.key_password.as_str())
                    };
                    
                    match self.qpe.load_key(Path::new(&self.private_key_path), key_pwd) {
                        Ok((priv_key, _)) => {
                            self.qpe.decrypt_folder(
                                input_path,
                                &output_path,
                                Some(&priv_key),
                                None,
                                true, // Show progress
                            )
                        }
                        Err(e) => {
                            // Provide more specific error messages
                            let error_msg = if e.to_string().contains("Wrong password") {
                                "Failed to decrypt private key. Check your key password.".to_string()
                            } else if e.to_string().contains("No such file") {
                                format!("Private key file not found: {}", self.private_key_path)
                            } else {
                                format!("Key loading failed: {}", e)
                            };
                            self.state = AppState::Error(error_msg);
                            return Ok(false);
                        }
                    }
                };

                match result {
                    Ok(_) => {
                        self.state = AppState::Success(
                            format!("✓ Archive decrypted successfully!\nOutput folder: {}", output_path.display())
                        );
                    }
                    Err(e) => {
                        // Provide more helpful error messages
                        let error_msg = if e.to_string().contains("Wrong password") {
                            "Decryption failed: Incorrect password".to_string()
                        } else if e.to_string().contains("ZIP") {
                            "Decryption failed: Invalid or corrupted archive file".to_string()
                        } else if e.to_string().contains("Permission denied") {
                            format!("Decryption failed: Permission denied. Check write access to: {}", output_path.display())
                        } else {
                            format!("Decryption failed: {}", e)
                        };
                        self.state = AppState::Error(error_msg);
                    }
                }
            }
            KeyCode::Backspace => {
                match self.input_field {
                    InputField::InputPath => { self.input_path.pop(); }
                    InputField::OutputPath => { self.output_path.pop(); }
                    InputField::PrivateKeyPath => { self.private_key_path.pop(); }
                    InputField::Password => { self.password.pop(); }
                    InputField::KeyPassword => { self.key_password.pop(); }
                    _ => {}
                }
            }
            KeyCode::Char(c) => {
                match self.input_field {
                    InputField::InputPath => self.input_path.push(c),
                    InputField::OutputPath => self.output_path.push(c),
                    InputField::PrivateKeyPath => self.private_key_path.push(c),
                    InputField::Password => self.password.push(c),
                    InputField::KeyPassword => self.key_password.push(c),
                    _ => {}
                }
            }
            _ => {}
        }
        Ok(false)
    }

    fn show_help(&mut self) {
        self.state = AppState::Success(
            "Normans Quantum-Proof Encryption v4.0\n\n\
            Navigation:\n\
            - Use ↑/↓ arrows to navigate menus\n\
            - Press Enter to select\n\
            - Press Tab to switch between fields\n\
            - Press Esc to go back\n\
            - Press Q to quit\n\n\
            Security Levels:\n\
            - Standard: Fast, suitable for most uses\n\
            - High: Recommended default\n\
            - Maximum: Highest security, slower\n\n\
            Tips:\n\
            - Always backup your private keys\n\
            - Use strong passwords for key encryption\n\
            - Keep your keys secure and private".to_string()
        );
    }
}