use crate::errors::Result;
use base64::{engine::general_purpose, Engine as _};
use rand::Rng;
use sha3::{Digest, Sha3_256};
use std::fs;
use std::path::Path;

/// Generate cryptographically secure random bytes
pub fn generate_random_bytes(size: usize) -> Vec<u8> {
    let mut bytes = vec![0u8; size];
    rand::thread_rng().fill(&mut bytes[..]);
    bytes
}

/// Generate a secure password
pub fn generate_secure_password(length: usize, include_symbols: bool, readable: bool) -> String {
    let mut rng = rand::thread_rng();
    
    let alphabet = if readable {
        if include_symbols {
            "abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789!@#$%^&*-_=+"
        } else {
            "abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789"
        }
    } else {
        if include_symbols {
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
        } else {
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        }
    };
    
    let chars: Vec<char> = alphabet.chars().collect();
    (0..length)
        .map(|_| chars[rng.gen_range(0..chars.len())])
        .collect()
}

/// Calculate fingerprint from public key
pub fn calculate_fingerprint(public_key: &[u8]) -> String {
    let mut hasher = Sha3_256::new();
    hasher.update(public_key);
    let digest = hasher.finalize();
    let hex = hex::encode(digest);
    
    // Format as colon-separated pairs
    hex.chars()
        .collect::<Vec<_>>()
        .chunks(2)
        .take(16) // First 32 hex chars
        .map(|chunk| chunk.iter().collect::<String>())
        .collect::<Vec<_>>()
        .join(":")
}

/// Calculate integrity check for key data
pub fn calculate_integrity_check(key: &[u8], metadata_json: &str) -> String {
    let mut hasher = Sha3_256::new();
    hasher.update(key);
    hasher.update(metadata_json.as_bytes());
    hex::encode(hasher.finalize())
}

/// Encode bytes to base64
pub fn encode_base64(data: &[u8]) -> String {
    general_purpose::STANDARD.encode(data)
}

/// Decode base64 to bytes
pub fn decode_base64(data: &str) -> Result<Vec<u8>> {
    Ok(general_purpose::STANDARD.decode(data)?)
}

/// Set file permissions (Unix-like systems only)
#[cfg(unix)]
pub fn set_file_permissions(path: &Path, mode: u32) -> Result<()> {
    use std::os::unix::fs::PermissionsExt;
    let permissions = fs::Permissions::from_mode(mode);
    fs::set_permissions(path, permissions)?;
    Ok(())
}

#[cfg(not(unix))]
pub fn set_file_permissions(_path: &Path, _mode: u32) -> Result<()> {
    Ok(())
}

/// Get file permissions as octal string (Unix-like systems only)
#[cfg(unix)]
pub fn get_file_permissions(path: &Path) -> Result<Option<String>> {
    use std::os::unix::fs::PermissionsExt;
    let metadata = fs::metadata(path)?;
    let mode = metadata.permissions().mode();
    Ok(Some(format!("{:o}", mode & 0o777)))
}

#[cfg(not(unix))]
pub fn get_file_permissions(_path: &Path) -> Result<Option<String>> {
    Ok(None)
}

/// Format bytes as human-readable size
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_idx = 0;
    
    while size >= 1024.0 && unit_idx < UNITS.len() - 1 {
        size /= 1024.0;
        unit_idx += 1;
    }
    
    if unit_idx == 0 {
        format!("{} {}", size as u64, UNITS[unit_idx])
    } else {
        format!("{:.2} {}", size, UNITS[unit_idx])
    }
}

/// Generate a random key ID
pub fn generate_key_id(public_key: &[u8]) -> String {
    let mut hasher = Sha3_256::new();
    hasher.update(public_key);
    let digest = hasher.finalize();
    hex::encode(&digest[..8])
}

/// Constant-time comparison for sensitive data
pub fn secure_compare(a: &[u8], b: &[u8]) -> bool {
    if a.len() != b.len() {
        return false;
    }
    
    let mut result = 0u8;
    for (byte_a, byte_b) in a.iter().zip(b.iter()) {
        result |= byte_a ^ byte_b;
    }
    
    result == 0
}

/// Securely wipe a file before deletion
pub fn secure_delete_file(path: &Path) -> Result<()> {
    if path.exists() {
        let file_size = fs::metadata(path)?.len();
        
        // Overwrite with random data
        let random_data = generate_random_bytes(1024);
        let mut file = fs::OpenOptions::new()
            .write(true)
            .open(path)?;
        
        use std::io::Write;
        let chunks = (file_size / 1024) + 1;
        for _ in 0..chunks {
            file.write_all(&random_data)?;
        }
        file.sync_all()?;
        drop(file);
        
        // Now delete
        fs::remove_file(path)?;
    }
    Ok(())
}

/// Print colored banner
pub fn print_banner() {
    use colored::*;
    
    println!();
    println!("{}", "╔═══════════════════════════════════════════════════════════════╗".cyan());
    println!("{} {} {}", "║".cyan(), "         Ultimate Quantum-Proof Encryption Program".bold().white(), "            ║".cyan());
    println!("{} {} {}", "║".cyan(), "             Version 4.0 - Streaming & Hardened".white(), "               ║".cyan());
    println!("{}", "╚═══════════════════════════════════════════════════════════════╝".cyan());
    println!();
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_generate_random_bytes() {
        let bytes = generate_random_bytes(32);
        assert_eq!(bytes.len(), 32);
        
        // Check that it's not all zeros
        assert!(bytes.iter().any(|&b| b != 0));
    }
    
    #[test]
    fn test_generate_secure_password() {
        let password = generate_secure_password(32, true, false);
        assert_eq!(password.len(), 32);
        
        let readable = generate_secure_password(16, false, true);
        assert_eq!(readable.len(), 16);
        // Check no confusing characters
        assert!(!readable.contains('0'));
        assert!(!readable.contains('O'));
        assert!(!readable.contains('l'));
        assert!(!readable.contains('I'));
    }
    
    #[test]
    fn test_base64_encoding() {
        let data = b"Hello, World!";
        let encoded = encode_base64(data);
        let decoded = decode_base64(&encoded).unwrap();
        assert_eq!(data, decoded.as_slice());
    }
    
    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(512), "512 B");
        assert_eq!(format_bytes(1024), "1.00 KB");
        assert_eq!(format_bytes(1536), "1.50 KB");
        assert_eq!(format_bytes(1048576), "1.00 MB");
    }
}