#[cfg(feature = "web-server")]
use std::collections::HashMap;
#[cfg(feature = "web-server")]
use std::path::Path;
#[cfg(feature = "web-server")]
use std::sync::Arc;


#[cfg(feature = "web-server")]
use serde::{Deserialize, Serialize};
#[cfg(feature = "web-server")]
use tokio::sync::Mutex;
#[cfg(feature = "web-server")]
use warp::{Filter, Reply, multipart::{FormData, Part}};
#[cfg(feature = "web-server")]
use futures_util::{TryStreamExt, StreamExt};

#[cfg(feature = "web-server")]
use crate::{QuantumProofEncryption, SecurityLevel};

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct EncryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "public-key", "password"
    input: String,          // text content for text operations
    security_level: Option<String>,
    compression_level: Option<u32>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug)]
struct FileUploadRequest {
    operation_type: String,
    method: String,
    security_level: String,
    compression_level: Option<i32>,
    password: Option<String>,
    output_path: Option<String>,
    files: Vec<(String, Vec<u8>)>, // (filename, content)
    public_key: Option<(String, Vec<u8>)>, // (filename, content)
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct DecryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "private-key", "password"
    input: String,          // file path or encrypted text
    private_key_path: Option<String>,
    key_password: Option<String>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct GenerateKeysRequest {
    security_level: String,
    public_key_output: String,
    private_key_output: String,
    key_password: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Serialize)]
struct ApiResponse {
    success: bool,
    message: String,
    data: Option<serde_json::Value>,
}

#[cfg(feature = "web-server")]
impl ApiResponse {
    fn success(message: &str, data: Option<serde_json::Value>) -> Self {
        Self {
            success: true,
            message: message.to_string(),
            data,
        }
    }

    fn error(message: &str) -> Self {
        Self {
            success: false,
            message: message.to_string(),
            data: None,
        }
    }
}

#[cfg(feature = "web-server")]
#[derive(Debug, Clone)]
struct DownloadFile {
    filename: String,
    content: Vec<u8>,
    content_type: String,
    created_at: std::time::SystemTime,
}

#[cfg(feature = "web-server")]
type SharedState = Arc<Mutex<HashMap<String, DownloadFile>>>;

#[cfg(feature = "web-server")]
pub async fn start_web_server(port: u16) -> Result<(), Box<dyn std::error::Error>> {
    let state: SharedState = Arc::new(Mutex::new(HashMap::new()));

    // CORS headers
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["content-type"])
        .allow_methods(vec!["GET", "POST", "OPTIONS"]);

    // Static files route - serve CSS, JS, and other assets
    let static_files = warp::fs::dir("web_frontend");

    // Root route - serve index.html
    let index = warp::path::end()
        .and(warp::fs::file("web_frontend/index.html"));

    // API routes
    let api = warp::path("api");

    // Encrypt endpoint (for text)
    let encrypt = api
        .and(warp::path("encrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_encrypt);

    // File upload endpoint (for files/folders)
    let encrypt_upload = api
        .and(warp::path("encrypt-upload"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(100 * 1024 * 1024)) // 100MB limit
        .and(with_state(state.clone()))
        .and_then(handle_encrypt_upload);

    // Decrypt endpoint
    let decrypt = api
        .and(warp::path("decrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_decrypt);

    // Generate keys endpoint
    let generate_keys = api
        .and(warp::path("generate-keys"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_keys);

    // Generate password endpoint
    let generate_password = api
        .and(warp::path("generate-password"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_password);

    // Download endpoint
    let download = api
        .and(warp::path("download"))
        .and(warp::path::param::<String>())
        .and(warp::get())
        .and(with_state(state.clone()))
        .and_then(handle_download);

    let routes = encrypt
        .or(encrypt_upload)
        .or(decrypt)
        .or(generate_keys)
        .or(generate_password)
        .or(download)
        .or(index)
        .or(static_files)
        .with(cors);

    println!("🌐 Starting Normans Quantum-Proof Encryption Web Server");
    println!("📍 Server running at: http://localhost:{}", port);
    println!("🔗 Open your browser and navigate to the URL above");
    println!("🛑 Press Ctrl+C to stop the server");

    warp::serve(routes)
        .run(([127, 0, 0, 1], port))
        .await;

    Ok(())
}

#[cfg(feature = "web-server")]
fn with_state(state: SharedState) -> impl Filter<Extract = (SharedState,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || state.clone())
}

#[cfg(feature = "web-server")]
async fn handle_encrypt(request: EncryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_encryption(request).await {
        Ok(result) => ApiResponse::success("Encryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_encrypt_upload(form: FormData, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_file_upload(form, state).await {
        Ok(result) => ApiResponse::success("Encryption completed successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_decrypt(request: DecryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_decryption(request).await {
        Ok(result) => ApiResponse::success("Decryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Decryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_generate_keys(request: GenerateKeysRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_key_generation(request).await {
        Ok(result) => ApiResponse::success("Keys generated successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Key generation failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_generate_password(request: serde_json::Value, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let length = request.get("length").and_then(|v| v.as_u64()).unwrap_or(32) as usize;

    let password = crate::generate_secure_password(length, true, false);

    let response = ApiResponse::success("Password generated successfully", Some(serde_json::json!({
        "password": password
    })));

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_download(file_id: String, state: SharedState) -> Result<Box<dyn Reply>, warp::Rejection> {
    let state_guard = state.lock().await;

    if let Some(download_file) = state_guard.get(&file_id) {
        let file = download_file.clone();
        drop(state_guard);

        let reply = warp::reply::with_header(
            warp::reply::with_header(
                file.content,
                "content-type",
                file.content_type,
            ),
            "content-disposition",
            format!("attachment; filename=\"{}\"", file.filename),
        );

        Ok(Box::new(reply))
    } else {
        let error_reply = warp::reply::with_status(
            warp::reply::with_header(
                "File not found or expired".as_bytes().to_vec(),
                "content-type",
                "text/plain",
            ),
            warp::http::StatusCode::NOT_FOUND,
        );

        Ok(Box::new(error_reply))
    }
}

#[cfg(feature = "web-server")]
async fn process_file_upload(mut form: FormData, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut upload_request = FileUploadRequest {
        operation_type: String::new(),
        method: String::new(),
        security_level: "high".to_string(),
        compression_level: None,
        password: None,
        output_path: None,
        files: Vec::new(),
        public_key: None,
    };

    // Process form data
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "operation_type" => {
                upload_request.operation_type = get_part_text(part).await?;
            }
            "method" => {
                upload_request.method = get_part_text(part).await?;
            }
            "security_level" => {
                upload_request.security_level = get_part_text(part).await?;
            }
            "compression_level" => {
                let text = get_part_text(part).await?;
                upload_request.compression_level = text.parse().ok();
            }
            "password" => {
                upload_request.password = Some(get_part_text(part).await?);
            }
            "output_path" => {
                upload_request.output_path = Some(get_part_text(part).await?);
            }
            "file" => {
                let filename = part.filename().unwrap_or("unknown").to_string();
                let content = get_part_bytes(part).await?;
                upload_request.files.push((filename, content));
            }
            "public_key" => {
                let filename = part.filename().unwrap_or("key.pub").to_string();
                let content = get_part_bytes(part).await?;
                upload_request.public_key = Some((filename, content));
            }
            _ => {
                // Skip unknown fields
            }
        }
    }

    perform_file_encryption(upload_request, state).await
}

#[cfg(feature = "web-server")]
async fn get_part_text(part: Part) -> Result<String, anyhow::Error> {
    let bytes = get_part_bytes(part).await?;
    String::from_utf8(bytes).map_err(|e| anyhow::anyhow!("Invalid UTF-8: {}", e))
}

#[cfg(feature = "web-server")]
async fn get_part_bytes(part: Part) -> Result<Vec<u8>, anyhow::Error> {
    use bytes::Buf;

    let stream = part.stream();
    let bytes: Result<Vec<_>, _> = stream.try_collect().await;
    let bytes = bytes.map_err(|e| anyhow::anyhow!("Stream error: {}", e))?;

    // Flatten the bytes
    let mut result = Vec::new();
    for mut chunk in bytes {
        while chunk.has_remaining() {
            let len = chunk.remaining();
            let mut buf = vec![0; len];
            chunk.copy_to_slice(&mut buf);
            result.extend_from_slice(&buf);
        }
    }
    Ok(result)
}

#[cfg(feature = "web-server")]
async fn perform_file_encryption(request: FileUploadRequest, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    if request.files.is_empty() {
        return Err(anyhow::anyhow!("No files uploaded"));
    }

    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    // Create temporary directory for uploaded files
    let temp_dir = tempfile::tempdir()?;
    let temp_path = temp_dir.path();

    // Calculate original total size
    let original_size: usize = request.files.iter().map(|(_, content)| content.len()).sum();

    // Save uploaded files to temporary directory with enhanced compression
    let mut input_path = None;
    for (filename, content) in &request.files {
        let file_path = temp_path.join(filename);
        if let Some(parent) = file_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        std::fs::write(&file_path, content)?;

        if input_path.is_none() {
            input_path = Some(file_path);
        }
    }

    let input_path = input_path.ok_or_else(|| anyhow::anyhow!("No input file"))?;

    // Determine output path
    let output_filename = request.output_path.unwrap_or_else(|| {
        if request.files.len() > 1 {
            "encrypted_files.qpe".to_string()
        } else {
            format!("{}.qpe", input_path.file_name().unwrap().to_string_lossy())
        }
    });
    let output_path = temp_path.join(&output_filename);

    // Use maximum compression for 60%+ reduction
    let compression_level = if request.operation_type == "folder" || request.files.len() > 1 {
        Some(9) // Maximum compression for folders/multiple files
    } else {
        None // Single files don't use ZIP compression
    };

    // Handle encryption method
    if request.method == "public-key" {
        if let Some((key_filename, key_content)) = &request.public_key {
            // Save public key to temporary file
            let key_path = temp_path.join(key_filename);
            std::fs::write(&key_path, key_content)?;

            // Load the key
            let (pub_key, _) = qpe.load_key(&key_path, None)?;

            // Encrypt the file/folder
            if request.operation_type == "folder" || request.files.len() > 1 {
                // For multiple files, create a folder structure and encrypt as folder
                let folder_path = temp_path.join("upload_folder");
                std::fs::create_dir_all(&folder_path)?;

                for (filename, content) in &request.files {
                    let file_path = folder_path.join(filename);
                    if let Some(parent) = file_path.parent() {
                        std::fs::create_dir_all(parent)?;
                    }
                    std::fs::write(&file_path, content)?;
                }

                qpe.encrypt_folder(&folder_path, &output_path, Some(&pub_key), None, compression_level, true)?;
            } else {
                qpe.encrypt_file(&input_path, &output_path, Some(&pub_key), None, true)?;
            }
        } else {
            return Err(anyhow::anyhow!("Public key required but not provided"));
        }
    } else {
        let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;

        if request.operation_type == "folder" || request.files.len() > 1 {
            // For multiple files, create a folder structure and encrypt as folder
            let folder_path = temp_path.join("upload_folder");
            std::fs::create_dir_all(&folder_path)?;

            for (filename, content) in &request.files {
                let file_path = folder_path.join(filename);
                if let Some(parent) = file_path.parent() {
                    std::fs::create_dir_all(parent)?;
                }
                std::fs::write(&file_path, content)?;
            }

            qpe.encrypt_folder(&folder_path, &output_path, None, Some(&password), compression_level, true)?;
        } else {
            qpe.encrypt_file(&input_path, &output_path, None, Some(&password), true)?;
        }
    }

    // Read the encrypted file for download
    let encrypted_content = std::fs::read(&output_path)?;
    let encrypted_size = encrypted_content.len();

    // Calculate compression ratio
    let compression_ratio = if original_size > 0 {
        ((original_size as f64 - encrypted_size as f64) / original_size as f64 * 100.0).max(0.0)
    } else {
        0.0
    };

    // Generate unique download ID
    let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());

    // Store file for download
    let download_file = DownloadFile {
        filename: output_filename.clone(),
        content: encrypted_content,
        content_type: "application/octet-stream".to_string(),
        created_at: std::time::SystemTime::now(),
    };

    {
        let mut state_guard = state.lock().await;
        state_guard.insert(download_id.clone(), download_file);

        // Clean up old downloads (older than 1 hour)
        let one_hour_ago = std::time::SystemTime::now() - std::time::Duration::from_secs(3600);
        state_guard.retain(|_, file| file.created_at > one_hour_ago);
    }

    Ok(serde_json::json!({
        "message": "File encrypted successfully!",
        "original_files": request.files.len(),
        "original_size": original_size,
        "encrypted_size": encrypted_size,
        "compression_ratio": format!("{:.1}%", compression_ratio),
        "output_filename": output_filename,
        "download_id": download_id,
        "download_url": format!("/api/download/{}", download_id),
        "compression_info": if compression_level.is_some() {
            format!("Maximum compression applied (level 9) - Achieved {:.1}% size reduction", compression_ratio)
        } else {
            "Single file encryption (no ZIP compression)".to_string()
        }
    }))
}

#[cfg(feature = "web-server")]
async fn perform_encryption(request: EncryptRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_deref() {
        Some("standard") => SecurityLevel::Standard,
        Some("high") => SecurityLevel::High,
        Some("maximum") => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "public-key" {
                return Err(anyhow::anyhow!("Text encryption with public key requires file upload. Use the file upload endpoint."));
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let encrypted = qpe.encrypt_text(&request.input, None, Some(&password))?;
                Ok(encrypted)
            }
        }
        _ => Err(anyhow::anyhow!("File and folder operations require file upload. Use the file upload endpoint.")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_decryption(request: DecryptRequest) -> Result<String, anyhow::Error> {
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                let decrypted = qpe.decrypt_text(&request.input, Some(&priv_key), None)?;
                Ok(decrypted)
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let decrypted = qpe.decrypt_text(&request.input, None, Some(&password))?;
                Ok(decrypted)
            }
        }
        "file" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("File decrypted successfully: {}", output_path))
        }
        "folder" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("Archive decrypted successfully: {}", output_path))
        }
        _ => Err(anyhow::anyhow!("Invalid operation type")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_key_generation(request: GenerateKeysRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);
    let (pub_key, priv_key) = qpe.generate_keypair()?;

    qpe.save_keypair(&pub_key, &priv_key, Path::new(&request.public_key_output), Path::new(&request.private_key_output), request.key_password.as_deref())?;

    Ok(format!(
        "Keypair generated successfully:\nPublic key: {}\nPrivate key: {}",
        request.public_key_output, request.private_key_output
    ))
}

#[cfg(not(feature = "web-server"))]
pub async fn start_web_server(_port: u16) -> Result<(), Box<dyn std::error::Error>> {
    Err("Web server feature not enabled. Compile with --features web-server".into())
}
