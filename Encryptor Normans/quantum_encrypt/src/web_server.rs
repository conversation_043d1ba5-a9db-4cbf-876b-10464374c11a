#[cfg(feature = "web-server")]
use std::collections::HashMap;
#[cfg(feature = "web-server")]
use std::path::Path;
#[cfg(feature = "web-server")]
use std::sync::Arc;


#[cfg(feature = "web-server")]
use serde::{Deserialize, Serialize};
#[cfg(feature = "web-server")]
use tokio::sync::Mutex;
#[cfg(feature = "web-server")]
use warp::{Filter, Reply, multipart::{FormData, Part}};
#[cfg(feature = "web-server")]
use futures_util::{TryStreamExt, StreamExt};
#[cfg(feature = "web-server")]
use tokio_util::codec::{BytesCodec, FramedRead};
#[cfg(feature = "web-server")]
use warp::hyper::Body;
#[cfg(feature = "web-server")]
use tokio::fs::File;

#[cfg(feature = "web-server")]
use crate::{QuantumProofEncryption, SecurityLevel};

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct EncryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "public-key", "password"
    input: String,          // text content for text operations
    security_level: Option<String>,
    compression_level: Option<u32>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug)]
struct FileUploadRequest {
    operation_type: String,
    method: String,
    security_level: String,
    compression_level: Option<i32>,
    password: Option<String>,
    output_path: Option<String>,
    files: Vec<(String, Vec<u8>)>, // (filename, content)
    public_key: Option<(String, Vec<u8>)>, // (filename, content)
}

#[cfg(feature = "web-server")]
#[derive(Debug)]
struct FileDecryptRequest {
    operation_type: String,
    method: String,
    password: Option<String>,
    key_password: Option<String>,
    output_path: Option<String>,
    encrypted_files: Vec<(String, Vec<u8>)>, // (filename, content)
    private_key: Option<(String, Vec<u8>)>, // (filename, content)
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct ChunkUploadRequest {
    upload_id: String,
    chunk_index: u32,
    total_chunks: u32,
    filename: String,
    file_size: u64,
    operation_type: String,
    method: String,
    security_level: Option<String>,
    password: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct ChunkCompleteRequest {
    upload_id: String,
    operation_type: String,
    method: String,
    security_level: String,
    compression_level: Option<i32>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Clone)]
struct ChunkUploadSession {
    upload_id: String,
    filename: String,
    file_size: u64,
    total_chunks: u32,
    received_chunks: std::collections::HashSet<u32>,
    temp_dir: std::path::PathBuf,
    created_at: std::time::SystemTime,
    operation_type: String,
    method: String,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct DecryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "private-key", "password"
    input: String,          // file path or encrypted text
    private_key_path: Option<String>,
    key_password: Option<String>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct GenerateKeysRequest {
    security_level: String,
    public_key_output: String,
    private_key_output: String,
    key_password: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Serialize)]
struct ApiResponse {
    success: bool,
    message: String,
    data: Option<serde_json::Value>,
}

#[cfg(feature = "web-server")]
impl ApiResponse {
    fn success(message: &str, data: Option<serde_json::Value>) -> Self {
        Self {
            success: true,
            message: message.to_string(),
            data,
        }
    }

    fn error(message: &str) -> Self {
        Self {
            success: false,
            message: message.to_string(),
            data: None,
        }
    }
}

#[cfg(feature = "web-server")]
#[derive(Debug, Clone)]
struct DownloadFile {
    filename: String,
    file_path: Option<std::path::PathBuf>, // For large files, store path instead of content
    content: Option<Vec<u8>>, // For small files, store content in memory
    content_type: String,
    created_at: std::time::SystemTime,
    file_size: u64,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Clone)]
struct ProgressInfo {
    operation_id: String,
    operation_type: String, // "encrypt" or "decrypt"
    stage: String, // "uploading", "processing", "compressing", "encrypting", "complete"
    current_file: String,
    files_processed: u32,
    total_files: u32,
    bytes_processed: u64,
    total_bytes: u64,
    percentage: f64,
    estimated_time_remaining: Option<u64>, // seconds
    compression_ratio: Option<f64>,
    current_speed: Option<f64>, // MB/s
    started_at: std::time::SystemTime,
    last_updated: std::time::SystemTime,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Clone)]
struct ServerState {
    downloads: HashMap<String, DownloadFile>,
    chunk_sessions: HashMap<String, ChunkUploadSession>,
    progress_info: HashMap<String, ProgressInfo>,
}

#[cfg(feature = "web-server")]
impl ServerState {
    fn new() -> Self {
        Self {
            downloads: HashMap::new(),
            chunk_sessions: HashMap::new(),
            progress_info: HashMap::new(),
        }
    }
}

#[cfg(feature = "web-server")]
type SharedState = Arc<Mutex<ServerState>>;

#[cfg(feature = "web-server")]
pub async fn start_web_server(port: u16) -> Result<(), Box<dyn std::error::Error>> {
    let state: SharedState = Arc::new(Mutex::new(ServerState::new()));

    // Create persistent download directory
    let download_dir = std::path::Path::new("downloads");
    if !download_dir.exists() {
        std::fs::create_dir_all(download_dir)?;
    }

    // Set up better error handling and logging
    println!("🚀 Starting Normans Quantum-Proof Encryption Web Server");
    println!("💾 Memory-optimized for large file processing");
    println!("📁 Supports folders up to 25GB with streaming upload");
    println!("📥 Large file downloads use streaming to prevent crashes");

    // CORS headers
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["content-type"])
        .allow_methods(vec!["GET", "POST", "OPTIONS"]);

    // Static files route - serve CSS, JS, and other assets
    let static_files = warp::fs::dir("web_frontend");

    // Root route - serve index.html
    let index = warp::path::end()
        .and(warp::fs::file("web_frontend/index.html"));

    // API routes
    let api = warp::path("api");

    // Encrypt endpoint (for text)
    let encrypt = api
        .and(warp::path("encrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_encrypt);

    // File upload endpoint (for files/folders)
    let encrypt_upload = api
        .and(warp::path("encrypt-upload"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(25 * 1024 * 1024 * 1024)) // 25GB limit for enterprise files
        .and(with_state(state.clone()))
        .and_then(handle_encrypt_upload);

    // Decrypt endpoint (for text)
    let decrypt = api
        .and(warp::path("decrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_decrypt);

    // Decrypt upload endpoint (for files/folders)
    let decrypt_upload = api
        .and(warp::path("decrypt-upload"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(25 * 1024 * 1024 * 1024)) // 25GB limit for enterprise files
        .and(with_state(state.clone()))
        .and_then(handle_decrypt_upload);

    // Generate keys endpoint
    let generate_keys = api
        .and(warp::path("generate-keys"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_keys);

    // Generate password endpoint
    let generate_password = api
        .and(warp::path("generate-password"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_password);

    // Download endpoint
    let download = api
        .and(warp::path("download"))
        .and(warp::path::param::<String>())
        .and(warp::get())
        .and(with_state(state.clone()))
        .and_then(handle_download);

    // Key info endpoint
    let key_info = api
        .and(warp::path("key-info"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(10 * 1024 * 1024)) // 10MB limit for keys
        .and(with_state(state.clone()))
        .and_then(handle_key_info);

    // Chunked upload endpoints for very large files (20GB+)
    let chunk_upload = api
        .and(warp::path("chunk-upload"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(100 * 1024 * 1024)) // 100MB per chunk
        .and(with_state(state.clone()))
        .and_then(handle_chunk_upload);

    let chunk_complete = api
        .and(warp::path("chunk-complete"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_chunk_complete);

    // Large folder upload endpoint with streaming support
    let large_folder_upload = api
        .and(warp::path("large-folder-upload"))
        .and(warp::post())
        .and(warp::multipart::form().max_length(25 * 1024 * 1024 * 1024)) // 25GB limit
        .and(with_state(state.clone()))
        .and_then(handle_large_folder_upload);

    // Progress tracking endpoint
    let progress = api
        .and(warp::path("progress"))
        .and(warp::path::param::<String>())
        .and(warp::get())
        .and(with_state(state.clone()))
        .and_then(handle_progress);

    let routes = encrypt
        .or(encrypt_upload)
        .or(decrypt)
        .or(decrypt_upload)
        .or(generate_keys)
        .or(generate_password)
        .or(key_info)
        .or(chunk_upload)
        .or(chunk_complete)
        .or(large_folder_upload)
        .or(progress)
        .or(download)
        .or(index)
        .or(static_files)
        .with(cors);

    println!("🌐 Starting Normans Quantum-Proof Encryption Web Server");
    println!("📍 Server running at: http://localhost:{}", port);
    println!("🔗 Open your browser and navigate to the URL above");
    println!("🛑 Press Ctrl+C to stop the server");

    warp::serve(routes)
        .run(([127, 0, 0, 1], port))
        .await;

    Ok(())
}

#[cfg(feature = "web-server")]
fn with_state(state: SharedState) -> impl Filter<Extract = (SharedState,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || state.clone())
}

#[cfg(feature = "web-server")]
async fn handle_encrypt(request: EncryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_encryption(request).await {
        Ok(result) => ApiResponse::success("Encryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_encrypt_upload(form: FormData, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_file_upload(form, state).await {
        Ok(result) => ApiResponse::success("Encryption completed successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_decrypt(request: DecryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_decryption(request).await {
        Ok(result) => ApiResponse::success("Decryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Decryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_decrypt_upload(form: FormData, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_file_decryption(form, state).await {
        Ok(result) => ApiResponse::success("Decryption completed successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Decryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_key_info(form: FormData, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_key_info(form).await {
        Ok(result) => ApiResponse::success("Key information retrieved successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Key info failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_chunk_upload(form: FormData, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_chunk_upload(form, state).await {
        Ok(result) => ApiResponse::success("Chunk uploaded successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Chunk upload failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_chunk_complete(request: ChunkCompleteRequest, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_chunk_complete(request, state).await {
        Ok(result) => ApiResponse::success("File processing completed successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("File processing failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_large_folder_upload(form: FormData, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match process_large_folder_upload(form, state).await {
        Ok(result) => ApiResponse::success("Large folder encrypted successfully", Some(result)),
        Err(e) => ApiResponse::error(&format!("Large folder encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_progress(operation_id: String, state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let state_guard = state.lock().await;

    if let Some(progress) = state_guard.progress_info.get(&operation_id) {
        let progress_data = serde_json::json!({
            "operation_id": progress.operation_id,
            "operation_type": progress.operation_type,
            "stage": progress.stage,
            "current_file": progress.current_file,
            "files_processed": progress.files_processed,
            "total_files": progress.total_files,
            "bytes_processed": progress.bytes_processed,
            "total_bytes": progress.total_bytes,
            "percentage": progress.percentage,
            "estimated_time_remaining": progress.estimated_time_remaining,
            "compression_ratio": progress.compression_ratio,
            "current_speed": progress.current_speed,
            "elapsed_time": progress.started_at.elapsed().unwrap_or_default().as_secs(),
            "status": if progress.percentage >= 100.0 { "complete" } else { "in_progress" }
        });

        Ok(warp::reply::json(&ApiResponse::success("Progress retrieved", Some(progress_data))))
    } else {
        Ok(warp::reply::json(&ApiResponse::error("Operation not found")))
    }
}

#[cfg(feature = "web-server")]
async fn handle_generate_keys(request: GenerateKeysRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_key_generation(request).await {
        Ok(result) => ApiResponse::success("Keys generated successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Key generation failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_generate_password(request: serde_json::Value, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let length = request.get("length").and_then(|v| v.as_u64()).unwrap_or(32) as usize;

    let password = crate::generate_secure_password(length, true, false);

    let response = ApiResponse::success("Password generated successfully", Some(serde_json::json!({
        "password": password
    })));

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_download(file_id: String, state: SharedState) -> Result<Box<dyn Reply>, warp::Rejection> {
    let state_guard = state.lock().await;

    if let Some(download_file) = state_guard.downloads.get(&file_id) {
        let file = download_file.clone();
        drop(state_guard);

        // Handle large files with true streaming, small files with direct content
        if let Some(file_path) = &file.file_path {
            // Large file - use streaming response to prevent memory issues
            println!("📥 Streaming large file download: {} ({:.1} MB)", file.filename, file.file_size as f64 / (1024.0 * 1024.0));

            match create_streaming_response(file_path.clone(), file.filename.clone(), file.content_type.clone()).await {
                Ok(response) => Ok(Box::new(response)),
                Err(e) => {
                    println!("❌ Failed to create streaming response: {}", e);
                    let error_reply = warp::reply::with_status(
                        warp::reply::with_header(
                            format!("Failed to stream file: {}", e).as_bytes().to_vec(),
                            "content-type",
                            "text/plain",
                        ),
                        warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                    );
                    Ok(Box::new(error_reply))
                }
            }
        } else if let Some(content) = &file.content {
            // Small file - serve from memory
            let reply = warp::reply::with_header(
                warp::reply::with_header(
                    content.clone(),
                    "content-type",
                    file.content_type.clone(),
                ),
                "content-disposition",
                format!("attachment; filename=\"{}\"", file.filename),
            );
            Ok(Box::new(reply))
        } else {
            let error_reply = warp::reply::with_status(
                warp::reply::with_header(
                    "File content not available".as_bytes().to_vec(),
                    "content-type",
                    "text/plain",
                ),
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
            );
            Ok(Box::new(error_reply))
        }
    } else {
        let error_reply = warp::reply::with_status(
            warp::reply::with_header(
                "File not found or expired".as_bytes().to_vec(),
                "content-type",
                "text/plain",
            ),
            warp::http::StatusCode::NOT_FOUND,
        );

        Ok(Box::new(error_reply))
    }
}

#[cfg(feature = "web-server")]
async fn create_streaming_response(
    file_path: std::path::PathBuf,
    filename: String,
    content_type: String,
) -> Result<impl Reply, Box<dyn std::error::Error + Send + Sync>> {
    // Open file for streaming
    let file = File::open(&file_path).await?;
    let file_size = file.metadata().await?.len();

    // Create a stream from the file
    let stream = FramedRead::new(file, BytesCodec::new())
        .map(|result| {
            result.map(|bytes| bytes.freeze())
                .map_err(|e| {
                    println!("Stream error: {}", e);
                    std::io::Error::new(std::io::ErrorKind::Other, e)
                })
        });

    // Convert to hyper body
    let body = Body::wrap_stream(stream);

    // Create response with proper headers
    let response = warp::http::Response::builder()
        .header("content-type", content_type)
        .header("content-disposition", format!("attachment; filename=\"{}\"", filename))
        .header("content-length", file_size.to_string())
        .header("accept-ranges", "bytes")
        .body(body)
        .map_err(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>)?;

    println!("✅ Created streaming response for {} ({:.1} MB)", filename, file_size as f64 / (1024.0 * 1024.0));

    Ok(response)
}

#[cfg(feature = "web-server")]
async fn process_file_upload(mut form: FormData, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut upload_request = FileUploadRequest {
        operation_type: String::new(),
        method: String::new(),
        security_level: "high".to_string(),
        compression_level: None,
        password: None,
        output_path: None,
        files: Vec::new(),
        public_key: None,
    };

    // Process form data
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "operation_type" => {
                upload_request.operation_type = get_part_text(part).await?;
            }
            "method" => {
                upload_request.method = get_part_text(part).await?;
            }
            "security_level" => {
                upload_request.security_level = get_part_text(part).await?;
            }
            "compression_level" => {
                let text = get_part_text(part).await?;
                upload_request.compression_level = text.parse().ok();
            }
            "password" => {
                upload_request.password = Some(get_part_text(part).await?);
            }
            "output_path" => {
                upload_request.output_path = Some(get_part_text(part).await?);
            }
            "file" => {
                let filename = part.filename().unwrap_or("unknown").to_string();
                let content = get_part_bytes(part).await?;
                upload_request.files.push((filename, content));
            }
            "public_key" => {
                let filename = part.filename().unwrap_or("key.pub").to_string();
                let content = get_part_bytes(part).await?;
                upload_request.public_key = Some((filename, content));
            }
            _ => {
                // Skip unknown fields
            }
        }
    }

    perform_file_encryption(upload_request, state).await
}

#[cfg(feature = "web-server")]
async fn process_file_decryption(mut form: FormData, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut decrypt_request = FileDecryptRequest {
        operation_type: String::new(),
        method: String::new(),
        password: None,
        key_password: None,
        output_path: None,
        encrypted_files: Vec::new(),
        private_key: None,
    };

    // Process form data
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "operation_type" => {
                decrypt_request.operation_type = get_part_text(part).await?;
            }
            "method" => {
                decrypt_request.method = get_part_text(part).await?;
            }
            "password" => {
                decrypt_request.password = Some(get_part_text(part).await?);
            }
            "key_password" => {
                decrypt_request.key_password = Some(get_part_text(part).await?);
            }
            "output_path" => {
                decrypt_request.output_path = Some(get_part_text(part).await?);
            }
            "encrypted_file" => {
                let filename = part.filename().unwrap_or("encrypted.qpe").to_string();
                let content = get_part_bytes(part).await?;
                decrypt_request.encrypted_files.push((filename, content));
            }
            "private_key" => {
                let filename = part.filename().unwrap_or("private.key").to_string();
                let content = get_part_bytes(part).await?;
                decrypt_request.private_key = Some((filename, content));
            }
            _ => {
                // Skip unknown fields
            }
        }
    }

    perform_file_decryption(decrypt_request, state).await
}

#[cfg(feature = "web-server")]
async fn process_chunk_upload(mut form: FormData, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut upload_id = String::new();
    let mut chunk_index = 0u32;
    let mut total_chunks = 0u32;
    let mut filename = String::new();
    let mut file_size = 0u64;
    let mut operation_type = String::new();
    let mut method = String::new();
    let mut chunk_data: Option<Vec<u8>> = None;

    // Process form data
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "upload_id" => upload_id = get_part_text(part).await?,
            "chunk_index" => chunk_index = get_part_text(part).await?.parse()?,
            "total_chunks" => total_chunks = get_part_text(part).await?.parse()?,
            "filename" => filename = get_part_text(part).await?,
            "file_size" => file_size = get_part_text(part).await?.parse()?,
            "operation_type" => operation_type = get_part_text(part).await?,
            "method" => method = get_part_text(part).await?,
            "chunk_data" => chunk_data = Some(get_part_bytes(part).await?),
            _ => {} // Skip unknown fields
        }
    }

    let chunk_data = chunk_data.ok_or_else(|| anyhow::anyhow!("No chunk data provided"))?;

    // Get or create upload session
    let mut state_guard = state.lock().await;

    let session = if let Some(existing_session) = state_guard.chunk_sessions.get_mut(&upload_id) {
        existing_session
    } else {
        // Create new session
        let temp_dir = tempfile::tempdir()?.into_path();
        let session = ChunkUploadSession {
            upload_id: upload_id.clone(),
            filename: filename.clone(),
            file_size,
            total_chunks,
            received_chunks: std::collections::HashSet::new(),
            temp_dir,
            created_at: std::time::SystemTime::now(),
            operation_type: operation_type.clone(),
            method: method.clone(),
        };
        state_guard.chunk_sessions.insert(upload_id.clone(), session);
        state_guard.chunk_sessions.get_mut(&upload_id).unwrap()
    };

    // Save chunk to temporary file
    let chunk_path = session.temp_dir.join(format!("chunk_{:06}", chunk_index));
    std::fs::write(&chunk_path, &chunk_data)?;
    session.received_chunks.insert(chunk_index);

    let is_complete = session.received_chunks.len() == total_chunks as usize;
    let progress = (session.received_chunks.len() as f64 / total_chunks as f64 * 100.0) as u32;

    Ok(serde_json::json!({
        "upload_id": upload_id,
        "chunk_index": chunk_index,
        "received_chunks": session.received_chunks.len(),
        "total_chunks": total_chunks,
        "progress": progress,
        "complete": is_complete,
        "message": if is_complete {
            "All chunks received. Ready for processing.".to_string()
        } else {
            format!("Chunk {}/{} uploaded successfully", session.received_chunks.len(), total_chunks)
        }
    }))
}

#[cfg(feature = "web-server")]
async fn process_chunk_complete(request: ChunkCompleteRequest, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut state_guard = state.lock().await;

    let session = state_guard.chunk_sessions.remove(&request.upload_id)
        .ok_or_else(|| anyhow::anyhow!("Upload session not found"))?;

    drop(state_guard);

    // Verify all chunks are received
    if session.received_chunks.len() != session.total_chunks as usize {
        return Err(anyhow::anyhow!(
            "Missing chunks: expected {}, got {}",
            session.total_chunks,
            session.received_chunks.len()
        ));
    }

    // Reassemble file from chunks
    let assembled_file_path = session.temp_dir.join(&session.filename);
    let mut assembled_file = std::fs::File::create(&assembled_file_path)?;

    for chunk_index in 0..session.total_chunks {
        let chunk_path = session.temp_dir.join(format!("chunk_{:06}", chunk_index));
        let chunk_data = std::fs::read(&chunk_path)?;
        std::io::Write::write_all(&mut assembled_file, &chunk_data)?;
        std::fs::remove_file(&chunk_path)?; // Clean up chunk
    }

    drop(assembled_file);

    // Now process the assembled file for encryption
    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);
    let original_size = session.file_size as usize;

    // Determine output path
    let output_filename = request.output_path.unwrap_or_else(|| {
        format!("{}.qpe", session.filename)
    });
    let output_path = session.temp_dir.join(&output_filename);

    // Use maximum compression for large files
    let _compression_level = request.compression_level.or(Some(9)); // Default to max compression

    // Encrypt the assembled file
    if request.method == "public-key" {
        return Err(anyhow::anyhow!("Public key encryption not yet supported for chunked uploads. Use password encryption."));
    } else {
        let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
        qpe.encrypt_file(&assembled_file_path, &output_path, None, Some(&password), true)?;
    }

    // Get encrypted file size and handle large files differently
    let encrypted_size = std::fs::metadata(&output_path)?.len() as usize;

    // Calculate compression ratio
    let compression_ratio = if original_size > 0 {
        ((original_size as f64 - encrypted_size as f64) / original_size as f64 * 100.0).max(0.0)
    } else {
        0.0
    };

    // Generate unique download ID
    let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());

    // For large files (>100MB), store path instead of content to prevent memory issues
    let download_file = if encrypted_size > 100 * 1024 * 1024 {
        // Large file - move to persistent download directory and store path
        let persistent_path = std::path::Path::new("downloads").join(&download_id);
        std::fs::copy(&output_path, &persistent_path)?;

        println!("📁 Large encrypted file ({:.1} MB) stored for streaming download", encrypted_size as f64 / (1024.0 * 1024.0));

        DownloadFile {
            filename: output_filename.clone(),
            file_path: Some(persistent_path),
            content: None,
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    } else {
        // Small file - store in memory
        let encrypted_content = std::fs::read(&output_path)?;

        DownloadFile {
            filename: output_filename.clone(),
            file_path: None,
            content: Some(encrypted_content),
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    };

    {
        let mut state_guard = state.lock().await;
        state_guard.downloads.insert(download_id.clone(), download_file);

        // Clean up old downloads (older than 1 hour)
        let one_hour_ago = std::time::SystemTime::now() - std::time::Duration::from_secs(3600);
        state_guard.downloads.retain(|_, file| file.created_at > one_hour_ago);
    }

    // Clean up temporary directory
    std::fs::remove_dir_all(&session.temp_dir).ok();

    Ok(serde_json::json!({
        "message": "Large file encrypted successfully!",
        "original_files": 1,
        "original_size": original_size,
        "encrypted_size": encrypted_size,
        "compression_ratio": format!("{:.1}%", compression_ratio),
        "output_filename": output_filename,
        "download_id": download_id,
        "download_url": format!("/api/download/{}", download_id),
        "compression_info": format!("Maximum compression applied - Achieved {:.1}% size reduction", compression_ratio),
        "upload_method": "chunked",
        "chunks_processed": session.total_chunks
    }))
}

#[cfg(feature = "web-server")]
async fn process_key_info(mut form: FormData) -> Result<serde_json::Value, anyhow::Error> {
    let mut key_file: Option<(String, Vec<u8>)> = None;
    let mut key_password: Option<String> = None;

    // Process form data
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "key_file" => {
                let filename = part.filename().unwrap_or("key.key").to_string();
                let content = get_part_bytes(part).await?;
                key_file = Some((filename, content));
            }
            "key_password" => {
                key_password = Some(get_part_text(part).await?);
            }
            _ => {
                // Skip unknown fields
            }
        }
    }

    let (filename, content) = key_file.ok_or_else(|| anyhow::anyhow!("No key file provided"))?;

    // Create temporary file for key analysis
    let temp_dir = tempfile::tempdir()?;
    let key_path = temp_dir.path().join(&filename);
    std::fs::write(&key_path, &content)?;

    // Analyze the key
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);

    match qpe.load_key(&key_path, key_password.as_deref()) {
        Ok((key_data, metadata)) => {
            let key_info = analyze_key_info(&key_data, &metadata, &filename, content.len());
            Ok(serde_json::json!({
                "key_info": key_info,
                "filename": filename,
                "file_size": content.len(),
                "status": "valid"
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "error": format!("Invalid key file: {}", e),
                "filename": filename,
                "file_size": content.len(),
                "status": "invalid"
            }))
        }
    }
}

#[cfg(feature = "web-server")]
fn analyze_key_info(key_data: &[u8], metadata: &crate::models::KeyMetadata, filename: &str, file_size: usize) -> String {
    let mut info = String::new();

    info.push_str(&format!("📄 Filename: {}\n", filename));
    info.push_str(&format!("📊 File Size: {} bytes\n", file_size));
    info.push_str(&format!("🔑 Algorithm: {}\n", metadata.algorithm));
    info.push_str(&format!("🔢 Key Length: {} bytes\n", key_data.len()));
    info.push_str(&format!("🛡️ Security Level: {}\n", metadata.security_level));
    info.push_str(&format!("📊 NIST Level: {}\n", metadata.nist_level));
    info.push_str(&format!("🔐 Variant: {}\n", metadata.variant.name()));
    info.push_str(&format!("🆔 Key ID: {}\n", metadata.key_id));
    info.push_str(&format!("👆 Fingerprint: {}\n", metadata.fingerprint));
    info.push_str(&format!("📅 Created: {}\n", metadata.created.format("%Y-%m-%d %H:%M:%S UTC")));

    // Determine key purpose
    if filename.contains("public") || filename.contains("pub") {
        info.push_str("🔓 Purpose: Public Key (for encryption)\n");
    } else if filename.contains("private") || filename.contains("priv") {
        info.push_str("🔒 Purpose: Private Key (for decryption)\n");
    } else {
        info.push_str("🔑 Purpose: Cryptographic Key\n");
    }

    // Encryption status
    if metadata.encrypted.unwrap_or(false) {
        info.push_str("🔒 Encryption: Protected with password\n");
    } else {
        info.push_str("🔓 Encryption: Unprotected (plaintext)\n");
    }

    info.push_str("✅ Status: Valid key file\n");
    info.push_str("🔐 Compatible with Normans Quantum-Proof Encryption");

    info
}

#[cfg(feature = "web-server")]
async fn perform_file_decryption(request: FileDecryptRequest, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    if request.encrypted_files.is_empty() {
        return Err(anyhow::anyhow!("No encrypted files uploaded"));
    }

    // Generate operation ID for progress tracking
    let operation_id = format!("decrypt_{}", chrono::Utc::now().timestamp_millis());

    // Initialize progress tracking for decryption
    let total_bytes: u64 = request.encrypted_files.iter().map(|(_, content)| content.len() as u64).sum();
    let mut progress = ProgressInfo {
        operation_id: operation_id.clone(),
        operation_type: "decrypt".to_string(),
        stage: "uploading".to_string(),
        current_file: "Processing encrypted files...".to_string(),
        files_processed: 0,
        total_files: request.encrypted_files.len() as u32,
        bytes_processed: 0,
        total_bytes,
        percentage: 0.0,
        estimated_time_remaining: None,
        compression_ratio: None,
        current_speed: None,
        started_at: std::time::SystemTime::now(),
        last_updated: std::time::SystemTime::now(),
    };

    // Store initial progress
    {
        let mut state_guard = state.lock().await;
        state_guard.progress_info.insert(operation_id.clone(), progress.clone());
    }

    println!("🔓 Starting large file decryption... (Operation ID: {})", operation_id);

    let qpe = QuantumProofEncryption::new(SecurityLevel::High);

    // Create temporary directory for processing
    let temp_dir = tempfile::tempdir()?;
    let temp_path = temp_dir.path();

    // Calculate original encrypted size
    let encrypted_size: usize = request.encrypted_files.iter().map(|(_, content)| content.len()).sum();

    // Save encrypted files to temporary directory with progress tracking
    let mut input_path = None;
    let mut processed_bytes = 0u64;

    for (i, (filename, content)) in request.encrypted_files.iter().enumerate() {
        progress.current_file = filename.clone();
        progress.files_processed = i as u32;
        progress.bytes_processed = processed_bytes;
        progress.percentage = (processed_bytes as f64 / total_bytes as f64 * 30.0).min(30.0); // 30% for upload

        // Update progress
        {
            let mut state_guard = state.lock().await;
            state_guard.progress_info.insert(operation_id.clone(), progress.clone());
        }

        let file_path = temp_path.join(filename);

        // For large files, write in chunks to prevent blocking
        if content.len() > 100 * 1024 * 1024 { // 100MB threshold
            let mut file = std::fs::File::create(&file_path)?;
            let chunk_size = 8 * 1024 * 1024; // 8MB chunks
            let mut written = 0;

            while written < content.len() {
                let end = std::cmp::min(written + chunk_size, content.len());
                std::io::Write::write_all(&mut file, &content[written..end])?;
                written = end;

                // Yield control periodically
                if written % (64 * 1024 * 1024) == 0 {
                    tokio::task::yield_now().await;
                }
            }
        } else {
            std::fs::write(&file_path, content)?;
        }

        processed_bytes += content.len() as u64;

        if input_path.is_none() {
            input_path = Some(file_path);
        }

        tokio::task::yield_now().await;
    }

    let input_path = input_path.ok_or_else(|| anyhow::anyhow!("No input file"))?;

    // Determine output path
    let output_filename = request.output_path.unwrap_or_else(|| {
        let input_name = input_path.file_stem().unwrap().to_string_lossy();
        if request.encrypted_files.len() > 1 {
            "decrypted_files".to_string()
        } else {
            format!("{}_decrypted", input_name)
        }
    });

    // Update progress for decryption stage
    progress.stage = "decrypting".to_string();
    progress.current_file = "Decrypting files...".to_string();
    progress.percentage = 40.0; // Upload complete, now decrypting

    {
        let mut state_guard = state.lock().await;
        state_guard.progress_info.insert(operation_id.clone(), progress.clone());
    }

    // Handle decryption method
    if request.method == "private-key" {
        if let Some((key_filename, key_content)) = &request.private_key {
            // Save private key to temporary file
            let key_path = temp_path.join(key_filename);
            std::fs::write(&key_path, key_content)?;

            // Load the key
            let (priv_key, _) = qpe.load_key(&key_path, request.key_password.as_deref())?;

            // Decrypt the file/folder
            if request.operation_type == "folder" || request.encrypted_files.len() > 1 {
                progress.current_file = "Decrypting folder archive...".to_string();
                progress.percentage = 60.0;
                {
                    let mut state_guard = state.lock().await;
                    state_guard.progress_info.insert(operation_id.clone(), progress.clone());
                }

                let output_path = temp_path.join(&output_filename);
                qpe.decrypt_folder(&input_path, &output_path, Some(&priv_key), None, true)?;

                // Update progress for ZIP creation
                progress.current_file = "Creating downloadable archive...".to_string();
                progress.percentage = 80.0;
                {
                    let mut state_guard = state.lock().await;
                    state_guard.progress_info.insert(operation_id.clone(), progress.clone());
                }

                // Create ZIP of decrypted folder for download
                let zip_path = temp_path.join(format!("{}.zip", output_filename));
                create_zip_archive(&output_path, &zip_path)?;

                let decrypted_size = std::fs::metadata(&zip_path)?.len() as usize;

                // Store for download with streaming support for large files
                let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());

                let download_file = if decrypted_size > 100 * 1024 * 1024 {
                    // Large file - move to persistent download directory and store path
                    let persistent_path = std::path::Path::new("downloads").join(&download_id);
                    std::fs::copy(&zip_path, &persistent_path)?;

                    println!("📁 Large decrypted folder ({:.1} MB) stored for streaming download", decrypted_size as f64 / (1024.0 * 1024.0));

                    DownloadFile {
                        filename: format!("{}.zip", output_filename),
                        file_path: Some(persistent_path),
                        content: None,
                        content_type: "application/zip".to_string(),
                        created_at: std::time::SystemTime::now(),
                        file_size: decrypted_size as u64,
                    }
                } else {
                    // Small file - store in memory
                    let decrypted_content = std::fs::read(&zip_path)?;

                    DownloadFile {
                        filename: format!("{}.zip", output_filename),
                        file_path: None,
                        content: Some(decrypted_content),
                        content_type: "application/zip".to_string(),
                        created_at: std::time::SystemTime::now(),
                        file_size: decrypted_size as u64,
                    }
                };

                // Complete progress tracking
                progress.stage = "complete".to_string();
                progress.current_file = "Decryption completed successfully".to_string();
                progress.percentage = 100.0;
                progress.estimated_time_remaining = Some(0);

                {
                    let mut state_guard = state.lock().await;
                    state_guard.downloads.insert(download_id.clone(), download_file);
                    state_guard.progress_info.insert(operation_id.clone(), progress.clone());
                }

                return Ok(serde_json::json!({
                    "message": "Folder decrypted successfully!",
                    "operation_id": operation_id,
                    "encrypted_size": encrypted_size,
                    "decrypted_size": decrypted_size,
                    "output_filename": format!("{}.zip", output_filename),
                    "download_id": download_id,
                    "download_url": format!("/api/download/{}", download_id),
                    "type": "folder"
                }));
            } else {
                let output_path = temp_path.join(&output_filename);
                qpe.decrypt_file(&input_path, &output_path, Some(&priv_key), None, true)?;

                let decrypted_content = std::fs::read(&output_path)?;
                let decrypted_size = decrypted_content.len();

                // Store for download
                let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());
                let download_file = DownloadFile {
                    filename: output_filename.clone(),
                    file_path: None,
                    content: Some(decrypted_content),
                    content_type: "application/octet-stream".to_string(),
                    created_at: std::time::SystemTime::now(),
                    file_size: decrypted_size as u64,
                };

                {
                    let mut state_guard = state.lock().await;
                    state_guard.downloads.insert(download_id.clone(), download_file);
                }

                return Ok(serde_json::json!({
                    "message": "File decrypted successfully!",
                    "encrypted_size": encrypted_size,
                    "decrypted_size": decrypted_size,
                    "output_filename": output_filename,
                    "download_id": download_id,
                    "download_url": format!("/api/download/{}", download_id),
                    "type": "file"
                }));
            }
        } else {
            return Err(anyhow::anyhow!("Private key required but not provided"));
        }
    } else {
        let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;

        if request.operation_type == "folder" || request.encrypted_files.len() > 1 {
            let output_path = temp_path.join(&output_filename);
            qpe.decrypt_folder(&input_path, &output_path, None, Some(&password), true)?;

            // Create ZIP of decrypted folder for download
            let zip_path = temp_path.join(format!("{}.zip", output_filename));
            create_zip_archive(&output_path, &zip_path)?;

            let decrypted_content = std::fs::read(&zip_path)?;
            let decrypted_size = decrypted_content.len();

            // Store for download
            let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());
            let download_file = DownloadFile {
                filename: format!("{}.zip", output_filename),
                file_path: None,
                content: Some(decrypted_content),
                content_type: "application/zip".to_string(),
                created_at: std::time::SystemTime::now(),
                file_size: decrypted_size as u64,
            };

            {
                let mut state_guard = state.lock().await;
                state_guard.downloads.insert(download_id.clone(), download_file);
            }

            Ok(serde_json::json!({
                "message": "Folder decrypted successfully!",
                "encrypted_size": encrypted_size,
                "decrypted_size": decrypted_size,
                "output_filename": format!("{}.zip", output_filename),
                "download_id": download_id,
                "download_url": format!("/api/download/{}", download_id),
                "type": "folder"
            }))
        } else {
            let output_path = temp_path.join(&output_filename);
            qpe.decrypt_file(&input_path, &output_path, None, Some(&password), true)?;

            let decrypted_content = std::fs::read(&output_path)?;
            let decrypted_size = decrypted_content.len();

            // Store for download
            let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());
            let download_file = DownloadFile {
                filename: output_filename.clone(),
                file_path: None,
                content: Some(decrypted_content),
                content_type: "application/octet-stream".to_string(),
                created_at: std::time::SystemTime::now(),
                file_size: decrypted_size as u64,
            };

            {
                let mut state_guard = state.lock().await;
                state_guard.downloads.insert(download_id.clone(), download_file);
            }

            Ok(serde_json::json!({
                "message": "File decrypted successfully!",
                "encrypted_size": encrypted_size,
                "decrypted_size": decrypted_size,
                "output_filename": output_filename,
                "download_id": download_id,
                "download_url": format!("/api/download/{}", download_id),
                "type": "file"
            }))
        }
    }
}

#[cfg(feature = "web-server")]
fn create_zip_archive(source_dir: &std::path::Path, output_path: &std::path::Path) -> Result<(), anyhow::Error> {
    use std::io::Write;

    let file = std::fs::File::create(output_path)?;
    let mut zip = zip::ZipWriter::new(file);

    let options = zip::write::FileOptions::default()
        .compression_method(zip::CompressionMethod::Deflated)
        .compression_level(Some(9));

    fn add_dir_to_zip(
        zip: &mut zip::ZipWriter<std::fs::File>,
        dir: &std::path::Path,
        prefix: &str,
        options: zip::write::FileOptions,
    ) -> Result<(), anyhow::Error> {
        for entry in std::fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            let name = path.file_name().unwrap().to_string_lossy();
            let full_name = if prefix.is_empty() {
                name.to_string()
            } else {
                format!("{}/{}", prefix, name)
            };

            if path.is_dir() {
                zip.add_directory(&full_name, options)?;
                add_dir_to_zip(zip, &path, &full_name, options)?;
            } else {
                zip.start_file(&full_name, options)?;
                let content = std::fs::read(&path)?;
                zip.write_all(&content)?;
            }
        }
        Ok(())
    }

    add_dir_to_zip(&mut zip, source_dir, "", options)?;
    zip.finish()?;

    Ok(())
}

#[cfg(feature = "web-server")]
async fn get_part_text(part: Part) -> Result<String, anyhow::Error> {
    let bytes = get_part_bytes(part).await?;
    String::from_utf8(bytes).map_err(|e| anyhow::anyhow!("Invalid UTF-8: {}", e))
}

#[cfg(feature = "web-server")]
async fn get_part_bytes(part: Part) -> Result<Vec<u8>, anyhow::Error> {
    use bytes::Buf;

    let stream = part.stream();
    let bytes: Result<Vec<_>, _> = stream.try_collect().await;
    let bytes = bytes.map_err(|e| anyhow::anyhow!("Stream error: {}", e))?;

    // Flatten the bytes
    let mut result = Vec::new();
    for mut chunk in bytes {
        while chunk.has_remaining() {
            let len = chunk.remaining();
            let mut buf = vec![0; len];
            chunk.copy_to_slice(&mut buf);
            result.extend_from_slice(&buf);
        }
    }
    Ok(result)
}

#[cfg(feature = "web-server")]
async fn perform_file_encryption(request: FileUploadRequest, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    if request.files.is_empty() {
        return Err(anyhow::anyhow!("No files uploaded"));
    }

    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    // Create temporary directory for uploaded files
    let temp_dir = tempfile::tempdir()?;
    let temp_path = temp_dir.path();

    // Calculate original total size
    let original_size: usize = request.files.iter().map(|(_, content)| content.len()).sum();

    // Save uploaded files to temporary directory with streaming for large files
    let mut input_path = None;
    let mut total_written = 0usize;

    for (filename, content) in &request.files {
        let file_path = temp_path.join(filename);
        if let Some(parent) = file_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // Stream large files to disk instead of keeping in memory
        if content.len() > 100 * 1024 * 1024 { // 100MB threshold
            let mut file = std::fs::File::create(&file_path)?;
            let mut written = 0;
            let chunk_size = 8 * 1024 * 1024; // 8MB chunks

            while written < content.len() {
                let end = std::cmp::min(written + chunk_size, content.len());
                std::io::Write::write_all(&mut file, &content[written..end])?;
                written = end;

                // Yield control to prevent blocking
                if written % (64 * 1024 * 1024) == 0 { // Every 64MB
                    tokio::task::yield_now().await;
                }
            }
        } else {
            std::fs::write(&file_path, content)?;
        }

        total_written += content.len();

        if input_path.is_none() {
            input_path = Some(file_path);
        }

        // Log progress for large folders
        if total_written % (500 * 1024 * 1024) == 0 { // Every 500MB
            println!("📁 Processed {} MB of folder data", total_written / (1024 * 1024));
        }
    }

    let input_path = input_path.ok_or_else(|| anyhow::anyhow!("No input file"))?;

    // Determine output path
    let output_filename = request.output_path.unwrap_or_else(|| {
        if request.files.len() > 1 {
            "encrypted_files.qpe".to_string()
        } else {
            format!("{}.qpe", input_path.file_name().unwrap().to_string_lossy())
        }
    });
    let output_path = temp_path.join(&output_filename);

    // Apply intelligent compression based on user selection
    let compression_level = if request.operation_type == "folder" || request.files.len() > 1 {
        let user_compression = request.compression_level.unwrap_or(6); // Default to high
        Some(match user_compression {
            1..=3 => 3,  // Low compression (fast, ~40-50% reduction)
            4..=6 => 6,  // High compression (balanced, ~60-70% reduction)
            7..=9 => 9,  // Maximum compression (best ratio, ~70-85% reduction)
            _ => 6,      // Default to high
        })
    } else {
        None // Single files don't use ZIP compression
    };

    // Handle encryption method
    if request.method == "public-key" {
        if let Some((key_filename, key_content)) = &request.public_key {
            // Save public key to temporary file
            let key_path = temp_path.join(key_filename);
            std::fs::write(&key_path, key_content)?;

            // Load the key
            let (pub_key, _) = qpe.load_key(&key_path, None)?;

            // Encrypt the file/folder
            if request.operation_type == "folder" || request.files.len() > 1 {
                // For multiple files, create a folder structure and encrypt as folder
                let folder_path = temp_path.join("upload_folder");
                std::fs::create_dir_all(&folder_path)?;

                for (filename, content) in &request.files {
                    let file_path = folder_path.join(filename);
                    if let Some(parent) = file_path.parent() {
                        std::fs::create_dir_all(parent)?;
                    }
                    std::fs::write(&file_path, content)?;
                }

                qpe.encrypt_folder(&folder_path, &output_path, Some(&pub_key), None, compression_level, true)?;
            } else {
                qpe.encrypt_file(&input_path, &output_path, Some(&pub_key), None, true)?;
            }
        } else {
            return Err(anyhow::anyhow!("Public key required but not provided"));
        }
    } else {
        let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;

        if request.operation_type == "folder" || request.files.len() > 1 {
            // For multiple files, create a folder structure and encrypt as folder
            let folder_path = temp_path.join("upload_folder");
            std::fs::create_dir_all(&folder_path)?;

            for (filename, content) in &request.files {
                let file_path = folder_path.join(filename);
                if let Some(parent) = file_path.parent() {
                    std::fs::create_dir_all(parent)?;
                }
                std::fs::write(&file_path, content)?;
            }

            qpe.encrypt_folder(&folder_path, &output_path, None, Some(&password), compression_level, true)?;
        } else {
            qpe.encrypt_file(&input_path, &output_path, None, Some(&password), true)?;
        }
    }

    // Get encrypted file size and handle large files differently
    let encrypted_size = std::fs::metadata(&output_path)?.len() as usize;

    // Calculate compression ratio
    let compression_ratio = if original_size > 0 {
        ((original_size as f64 - encrypted_size as f64) / original_size as f64 * 100.0).max(0.0)
    } else {
        0.0
    };

    // Generate unique download ID
    let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());

    // For large files (>100MB), store path instead of content to prevent memory issues
    let download_file = if encrypted_size > 100 * 1024 * 1024 {
        // Large file - move to persistent download directory and store path
        let persistent_path = std::path::Path::new("downloads").join(&download_id);
        std::fs::copy(&output_path, &persistent_path)?;

        println!("📁 Large encrypted file ({:.1} MB) stored for streaming download", encrypted_size as f64 / (1024.0 * 1024.0));

        DownloadFile {
            filename: output_filename.clone(),
            file_path: Some(persistent_path),
            content: None,
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    } else {
        // Small file - store in memory
        let encrypted_content = std::fs::read(&output_path)?;

        DownloadFile {
            filename: output_filename.clone(),
            file_path: None,
            content: Some(encrypted_content),
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    };

    {
        let mut state_guard = state.lock().await;
        state_guard.downloads.insert(download_id.clone(), download_file);

        // Clean up old downloads (older than 1 hour)
        let one_hour_ago = std::time::SystemTime::now() - std::time::Duration::from_secs(3600);
        state_guard.downloads.retain(|_, file| file.created_at > one_hour_ago);
    }

    Ok(serde_json::json!({
        "message": "File encrypted successfully!",
        "original_files": request.files.len(),
        "original_size": original_size,
        "encrypted_size": encrypted_size,
        "compression_ratio": format!("{:.1}%", compression_ratio),
        "output_filename": output_filename,
        "download_id": download_id,
        "download_url": format!("/api/download/{}", download_id),
        "compression_info": if compression_level.is_some() {
            format!("Maximum compression applied (level 9) - Achieved {:.1}% size reduction", compression_ratio)
        } else {
            "Single file encryption (no ZIP compression)".to_string()
        }
    }))
}

#[cfg(feature = "web-server")]
async fn process_large_folder_upload(mut form: FormData, state: SharedState) -> Result<serde_json::Value, anyhow::Error> {
    let mut upload_request = FileUploadRequest {
        operation_type: "folder".to_string(),
        method: String::new(),
        security_level: "high".to_string(),
        compression_level: Some(9), // Default to maximum compression for large folders
        password: None,
        output_path: None,
        files: Vec::new(),
        public_key: None,
    };

    let temp_dir = tempfile::tempdir()?;
    let temp_path = temp_dir.path();
    let folder_path = temp_path.join("large_folder");
    std::fs::create_dir_all(&folder_path)?;

    let mut total_files = 0;
    let mut total_size = 0u64;
    let mut processed_size = 0u64;

    // Generate operation ID for progress tracking
    let operation_id = format!("op_{}", chrono::Utc::now().timestamp_millis());

    // Initialize progress tracking
    let mut progress = ProgressInfo {
        operation_id: operation_id.clone(),
        operation_type: "encrypt".to_string(),
        stage: "uploading".to_string(),
        current_file: "Initializing...".to_string(),
        files_processed: 0,
        total_files: 0,
        bytes_processed: 0,
        total_bytes: 0,
        percentage: 0.0,
        estimated_time_remaining: None,
        compression_ratio: None,
        current_speed: None,
        started_at: std::time::SystemTime::now(),
        last_updated: std::time::SystemTime::now(),
    };

    println!("🚀 Starting large folder upload processing... (Operation ID: {})", operation_id);

    // Process form data with streaming
    while let Some(part) = form.next().await {
        let part = part.map_err(|e| anyhow::anyhow!("Form parsing error: {}", e))?;
        let name = part.name().to_string();

        match name.as_str() {
            "method" => {
                upload_request.method = get_part_text(part).await?;
            }
            "security_level" => {
                upload_request.security_level = get_part_text(part).await?;
            }
            "compression_level" => {
                let text = get_part_text(part).await?;
                upload_request.compression_level = text.parse().ok();
            }
            "password" => {
                upload_request.password = Some(get_part_text(part).await?);
            }
            "output_path" => {
                upload_request.output_path = Some(get_part_text(part).await?);
            }
            "public_key" => {
                let filename = part.filename().unwrap_or("key.pub").to_string();
                let content = get_part_bytes(part).await?;
                upload_request.public_key = Some((filename, content));
            }
            "file" => {
                let filename = part.filename().unwrap_or("unknown").to_string();

                // Stream large files directly to disk instead of loading into memory
                let file_path = folder_path.join(&filename);
                if let Some(parent) = file_path.parent() {
                    std::fs::create_dir_all(parent)?;
                }

                // Update progress for current file
                progress.current_file = filename.clone();
                progress.stage = "uploading".to_string();

                // Stream the file content
                let file_size = stream_part_to_file(part, &file_path).await?;
                total_files += 1;
                total_size += file_size;
                processed_size += file_size;

                // Update progress tracking
                progress.files_processed = total_files;
                progress.bytes_processed = processed_size;
                progress.total_bytes = total_size; // Will be updated as we discover more files
                progress.percentage = if total_size > 0 { (processed_size as f64 / total_size as f64 * 50.0).min(50.0) } else { 0.0 }; // 50% for upload
                progress.last_updated = std::time::SystemTime::now();

                // Calculate speed
                let elapsed = progress.started_at.elapsed().unwrap_or_default().as_secs_f64();
                if elapsed > 0.0 {
                    progress.current_speed = Some(processed_size as f64 / (1024.0 * 1024.0) / elapsed);

                    // Estimate time remaining
                    if progress.percentage > 0.0 {
                        let total_estimated_time = elapsed / (progress.percentage / 100.0);
                        progress.estimated_time_remaining = Some((total_estimated_time - elapsed) as u64);
                    }
                }

                // Update progress in state every 10 files or 100MB
                if total_files % 10 == 0 || processed_size % (100 * 1024 * 1024) == 0 {
                    {
                        let mut state_guard = state.lock().await;
                        state_guard.progress_info.insert(operation_id.clone(), progress.clone());
                    }
                    println!("📁 Progress: {} files, {:.1} GB ({:.1}%)", total_files, processed_size as f64 / (1024.0 * 1024.0 * 1024.0), progress.percentage);
                }

                // Yield control periodically to prevent blocking
                if total_files % 5 == 0 {
                    tokio::task::yield_now().await;
                }
            }
            _ => {
                // Skip unknown fields
            }
        }
    }

    println!("📊 Large folder upload complete: {} files, {:.2} GB", total_files, total_size as f64 / (1024.0 * 1024.0 * 1024.0));

    // Now encrypt the folder with memory-efficient processing
    let security_level = match upload_request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    // Determine output path
    let output_filename = upload_request.output_path.unwrap_or_else(|| "large_folder.qpe".to_string());
    let output_path = temp_path.join(&output_filename);

    // Update progress for encryption stage
    progress.stage = "encrypting".to_string();
    progress.current_file = "Compressing and encrypting folder...".to_string();
    progress.percentage = 60.0; // Upload complete, now encrypting
    progress.total_files = total_files;
    progress.total_bytes = total_size;

    {
        let mut state_guard = state.lock().await;
        state_guard.progress_info.insert(operation_id.clone(), progress.clone());
    }

    println!("🔒 Starting encryption of large folder... (Operation ID: {})", operation_id);

    // Encrypt the folder with maximum compression
    if upload_request.method == "public-key" {
        if let Some((key_filename, key_content)) = &upload_request.public_key {
            let key_path = temp_path.join(key_filename);
            std::fs::write(&key_path, key_content)?;
            let (pub_key, _) = qpe.load_key(&key_path, None)?;
            qpe.encrypt_folder(&folder_path, &output_path, Some(&pub_key), None, Some(9), true)?;
        } else {
            return Err(anyhow::anyhow!("Public key required but not provided"));
        }
    } else {
        let password = upload_request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
        qpe.encrypt_folder(&folder_path, &output_path, None, Some(&password), Some(9), true)?;
    }

    // Update progress for completion
    progress.stage = "complete".to_string();
    progress.current_file = "Encryption completed successfully".to_string();
    progress.percentage = 100.0;

    // Get encrypted file size and handle large files differently
    let encrypted_size = std::fs::metadata(&output_path)?.len() as usize;

    // Calculate compression ratio
    let compression_ratio = if total_size > 0 {
        ((total_size as f64 - encrypted_size as f64) / total_size as f64 * 100.0).max(0.0)
    } else {
        0.0
    };

    // Update final progress with compression info
    progress.compression_ratio = Some(compression_ratio);
    progress.estimated_time_remaining = Some(0);

    {
        let mut state_guard = state.lock().await;
        state_guard.progress_info.insert(operation_id.clone(), progress.clone());
    }

    println!("✅ Large folder encryption complete: {:.1}% compression achieved", compression_ratio);

    // Generate unique download ID
    let download_id = format!("download_{}", chrono::Utc::now().timestamp_millis());

    // For large files (>100MB), store path instead of content to prevent memory issues
    let download_file = if encrypted_size > 100 * 1024 * 1024 {
        // Large file - move to persistent download directory and store path
        let persistent_path = std::path::Path::new("downloads").join(&download_id);
        std::fs::copy(&output_path, &persistent_path)?;

        println!("📁 Large encrypted folder ({:.1} MB) stored for streaming download", encrypted_size as f64 / (1024.0 * 1024.0));

        DownloadFile {
            filename: output_filename.clone(),
            file_path: Some(persistent_path),
            content: None,
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    } else {
        // Small file - store in memory
        let encrypted_content = std::fs::read(&output_path)?;

        DownloadFile {
            filename: output_filename.clone(),
            file_path: None,
            content: Some(encrypted_content),
            content_type: "application/octet-stream".to_string(),
            created_at: std::time::SystemTime::now(),
            file_size: encrypted_size as u64,
        }
    };

    {
        let mut state_guard = state.lock().await;
        state_guard.downloads.insert(download_id.clone(), download_file);

        // Clean up old downloads (older than 1 hour)
        let one_hour_ago = std::time::SystemTime::now() - std::time::Duration::from_secs(3600);
        state_guard.downloads.retain(|_, file| file.created_at > one_hour_ago);
    }

    Ok(serde_json::json!({
        "message": "Large folder encrypted successfully!",
        "operation_id": operation_id,
        "original_files": total_files,
        "original_size": total_size,
        "encrypted_size": encrypted_size,
        "compression_ratio": format!("{:.1}%", compression_ratio),
        "output_filename": output_filename,
        "download_id": download_id,
        "download_url": format!("/api/download/{}", download_id),
        "compression_info": format!("Maximum compression applied to {} files - Achieved {:.1}% size reduction", total_files, compression_ratio),
        "upload_method": "streaming",
        "processing_notes": "Used memory-efficient streaming for large folder processing",
        "progress_url": format!("/api/progress/{}", operation_id)
    }))
}

#[cfg(feature = "web-server")]
async fn stream_part_to_file(part: warp::multipart::Part, file_path: &std::path::Path) -> Result<u64, anyhow::Error> {
    use futures_util::StreamExt;

    let mut file = std::fs::File::create(file_path)?;
    let mut stream = part.stream();
    let mut total_size = 0u64;

    while let Some(chunk_result) = stream.next().await {
        let chunk = chunk_result.map_err(|e| anyhow::anyhow!("Stream error: {}", e))?;

        // Write chunk to file
        use bytes::Buf;
        let mut remaining = chunk;
        while remaining.has_remaining() {
            let len = remaining.remaining();
            let mut buf = vec![0; len];
            remaining.copy_to_slice(&mut buf);
            std::io::Write::write_all(&mut file, &buf)?;
            total_size += len as u64;
        }

        // Yield control every 10MB to prevent blocking
        if total_size % (10 * 1024 * 1024) == 0 {
            tokio::task::yield_now().await;
        }
    }

    Ok(total_size)
}

#[cfg(feature = "web-server")]
async fn perform_encryption(request: EncryptRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_deref() {
        Some("standard") => SecurityLevel::Standard,
        Some("high") => SecurityLevel::High,
        Some("maximum") => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "public-key" {
                return Err(anyhow::anyhow!("Text encryption with public key requires file upload. Use the file upload endpoint."));
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let encrypted = qpe.encrypt_text(&request.input, None, Some(&password))?;
                Ok(encrypted)
            }
        }
        _ => Err(anyhow::anyhow!("File and folder operations require file upload. Use the file upload endpoint.")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_decryption(request: DecryptRequest) -> Result<String, anyhow::Error> {
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                let decrypted = qpe.decrypt_text(&request.input, Some(&priv_key), None)?;
                Ok(decrypted)
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let decrypted = qpe.decrypt_text(&request.input, None, Some(&password))?;
                Ok(decrypted)
            }
        }
        "file" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("File decrypted successfully: {}", output_path))
        }
        "folder" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("Archive decrypted successfully: {}", output_path))
        }
        _ => Err(anyhow::anyhow!("Invalid operation type")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_key_generation(request: GenerateKeysRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);
    let (pub_key, priv_key) = qpe.generate_keypair()?;

    qpe.save_keypair(&pub_key, &priv_key, Path::new(&request.public_key_output), Path::new(&request.private_key_output), request.key_password.as_deref())?;

    Ok(format!(
        "Keypair generated successfully:\nPublic key: {}\nPrivate key: {}",
        request.public_key_output, request.private_key_output
    ))
}

#[cfg(not(feature = "web-server"))]
pub async fn start_web_server(_port: u16) -> Result<(), Box<dyn std::error::Error>> {
    Err("Web server feature not enabled. Compile with --features web-server".into())
}
