#[cfg(feature = "web-server")]
use std::collections::HashMap;
#[cfg(feature = "web-server")]
use std::path::Path;
#[cfg(feature = "web-server")]
use std::sync::Arc;

#[cfg(feature = "web-server")]
use serde::{Deserialize, Serialize};
#[cfg(feature = "web-server")]
use tokio::sync::Mutex;
#[cfg(feature = "web-server")]
use warp::{Filter, Reply};

#[cfg(feature = "web-server")]
use crate::{QuantumProofEncryption, SecurityLevel};

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct EncryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "public-key", "password"
    input: String,          // file path or text content
    security_level: Option<String>,
    compression_level: Option<u32>,
    public_key_path: Option<String>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct DecryptRequest {
    operation_type: String, // "file", "folder", "text"
    method: String,         // "private-key", "password"
    input: String,          // file path or encrypted text
    private_key_path: Option<String>,
    key_password: Option<String>,
    password: Option<String>,
    output_path: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Deserialize)]
struct GenerateKeysRequest {
    security_level: String,
    public_key_output: String,
    private_key_output: String,
    key_password: Option<String>,
}

#[cfg(feature = "web-server")]
#[derive(Debug, Serialize)]
struct ApiResponse {
    success: bool,
    message: String,
    data: Option<serde_json::Value>,
}

#[cfg(feature = "web-server")]
impl ApiResponse {
    fn success(message: &str, data: Option<serde_json::Value>) -> Self {
        Self {
            success: true,
            message: message.to_string(),
            data,
        }
    }

    fn error(message: &str) -> Self {
        Self {
            success: false,
            message: message.to_string(),
            data: None,
        }
    }
}

#[cfg(feature = "web-server")]
type SharedState = Arc<Mutex<HashMap<String, String>>>;

#[cfg(feature = "web-server")]
pub async fn start_web_server(port: u16) -> Result<(), Box<dyn std::error::Error>> {
    let state: SharedState = Arc::new(Mutex::new(HashMap::new()));

    // CORS headers
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["content-type"])
        .allow_methods(vec!["GET", "POST", "OPTIONS"]);

    // Static files route
    let static_files = warp::path("static")
        .and(warp::fs::dir("web_frontend"));

    // Root route - serve index.html
    let index = warp::path::end()
        .and(warp::fs::file("web_frontend/index.html"));

    // API routes
    let api = warp::path("api");

    // Encrypt endpoint
    let encrypt = api
        .and(warp::path("encrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_encrypt);

    // Decrypt endpoint
    let decrypt = api
        .and(warp::path("decrypt"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_decrypt);

    // Generate keys endpoint
    let generate_keys = api
        .and(warp::path("generate-keys"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_keys);

    // Generate password endpoint
    let generate_password = api
        .and(warp::path("generate-password"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_state(state.clone()))
        .and_then(handle_generate_password);

    let routes = index
        .or(static_files)
        .or(encrypt)
        .or(decrypt)
        .or(generate_keys)
        .or(generate_password)
        .with(cors);

    println!("🌐 Starting Normans Quantum-Proof Encryption Web Server");
    println!("📍 Server running at: http://localhost:{}", port);
    println!("🔗 Open your browser and navigate to the URL above");
    println!("🛑 Press Ctrl+C to stop the server");

    warp::serve(routes)
        .run(([127, 0, 0, 1], port))
        .await;

    Ok(())
}

#[cfg(feature = "web-server")]
fn with_state(state: SharedState) -> impl Filter<Extract = (SharedState,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || state.clone())
}

#[cfg(feature = "web-server")]
async fn handle_encrypt(request: EncryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_encryption(request).await {
        Ok(result) => ApiResponse::success("Encryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Encryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_decrypt(request: DecryptRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_decryption(request).await {
        Ok(result) => ApiResponse::success("Decryption completed successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Decryption failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_generate_keys(request: GenerateKeysRequest, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let response = match perform_key_generation(request).await {
        Ok(result) => ApiResponse::success("Keys generated successfully", Some(serde_json::json!({
            "output": result
        }))),
        Err(e) => ApiResponse::error(&format!("Key generation failed: {}", e)),
    };

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn handle_generate_password(request: serde_json::Value, _state: SharedState) -> Result<impl Reply, warp::Rejection> {
    let length = request.get("length").and_then(|v| v.as_u64()).unwrap_or(32) as usize;
    
    let password = crate::generate_secure_password(length);
    
    let response = ApiResponse::success("Password generated successfully", Some(serde_json::json!({
        "password": password
    })));

    Ok(warp::reply::json(&response))
}

#[cfg(feature = "web-server")]
async fn perform_encryption(request: EncryptRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_deref() {
        Some("standard") => SecurityLevel::Standard,
        Some("high") => SecurityLevel::High,
        Some("maximum") => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "public-key" {
                let public_key_path = request.public_key_path.ok_or_else(|| anyhow::anyhow!("Public key path required"))?;
                let (pub_key, _) = qpe.load_key(Path::new(&public_key_path), None)?;
                let encrypted = qpe.encrypt_text(&request.input, Some(&pub_key), None)?;
                Ok(encrypted)
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let encrypted = qpe.encrypt_text(&request.input, None, Some(&password))?;
                Ok(encrypted)
            }
        }
        "file" => {
            let output_path = request.output_path.unwrap_or_else(|| format!("{}.qpe", request.input));
            
            if request.method == "public-key" {
                let public_key_path = request.public_key_path.ok_or_else(|| anyhow::anyhow!("Public key path required"))?;
                let (pub_key, _) = qpe.load_key(Path::new(&public_key_path), None)?;
                qpe.encrypt_file(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.encrypt_file(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("File encrypted successfully: {}", output_path))
        }
        "folder" => {
            let output_path = request.output_path.unwrap_or_else(|| format!("{}.qpe", request.input));
            
            if request.method == "public-key" {
                let public_key_path = request.public_key_path.ok_or_else(|| anyhow::anyhow!("Public key path required"))?;
                let (pub_key, _) = qpe.load_key(Path::new(&public_key_path), None)?;
                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("Folder encrypted successfully: {}", output_path))
        }
        _ => Err(anyhow::anyhow!("Invalid operation type")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_decryption(request: DecryptRequest) -> Result<String, anyhow::Error> {
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);

    match request.operation_type.as_str() {
        "text" => {
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                let decrypted = qpe.decrypt_text(&request.input, Some(&priv_key), None)?;
                Ok(decrypted)
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                let decrypted = qpe.decrypt_text(&request.input, None, Some(&password))?;
                Ok(decrypted)
            }
        }
        "file" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_file(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("File decrypted successfully: {}", output_path))
        }
        "folder" => {
            let output_path = request.output_path.unwrap_or_else(|| {
                let input_path = Path::new(&request.input);
                let stem = input_path.file_stem().and_then(|s| s.to_str()).unwrap_or("decrypted");
                format!("{}_decrypted", stem)
            });
            
            if request.method == "private-key" {
                let private_key_path = request.private_key_path.ok_or_else(|| anyhow::anyhow!("Private key path required"))?;
                let (priv_key, _) = qpe.load_key(Path::new(&private_key_path), request.key_password.as_deref())?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&priv_key), None, true)?;
            } else {
                let password = request.password.ok_or_else(|| anyhow::anyhow!("Password required"))?;
                qpe.decrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;
            }
            
            Ok(format!("Archive decrypted successfully: {}", output_path))
        }
        _ => Err(anyhow::anyhow!("Invalid operation type")),
    }
}

#[cfg(feature = "web-server")]
async fn perform_key_generation(request: GenerateKeysRequest) -> Result<String, anyhow::Error> {
    let security_level = match request.security_level.as_str() {
        "standard" => SecurityLevel::Standard,
        "high" => SecurityLevel::High,
        "maximum" => SecurityLevel::Maximum,
        _ => SecurityLevel::High,
    };

    let qpe = QuantumProofEncryption::new(security_level);
    let (pub_key, priv_key) = qpe.generate_keypair()?;

    qpe.save_key(&pub_key, Path::new(&request.public_key_output), None)?;
    qpe.save_key(&priv_key, Path::new(&request.private_key_output), request.key_password.as_deref())?;

    Ok(format!(
        "Keypair generated successfully:\nPublic key: {}\nPrivate key: {}",
        request.public_key_output, request.private_key_output
    ))
}

#[cfg(not(feature = "web-server"))]
pub async fn start_web_server(_port: u16) -> Result<(), Box<dyn std::error::Error>> {
    Err("Web server feature not enabled. Compile with --features web-server".into())
}
