#!/bin/bash

echo "🌐 Normans Quantum-Proof Encryption - Web Server Launcher"
echo "=========================================================="

# Check if binary exists
if [ ! -f "./target/release/normans-quantum-proof-encryption" ]; then
    echo "❌ Binary not found. Building with web server support..."
    cargo build --release --features web-server
    
    if [ $? -ne 0 ]; then
        echo "❌ Build failed. Please check the error messages above."
        exit 1
    fi
    echo "✅ Build completed successfully!"
fi

# Find available port
PORT=8080
while lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; do
    echo "⚠️  Port $PORT is in use, trying $((PORT+1))..."
    PORT=$((PORT+1))
done

echo ""
echo "🚀 Starting Normans Quantum-Proof Encryption Web Server"
echo "📍 Server will run on: http://localhost:$PORT"
echo "🔗 The browser should open automatically"
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Try to open browser (works on most systems)
if command -v xdg-open > /dev/null; then
    # Linux
    (sleep 2 && xdg-open "http://localhost:$PORT") &
elif command -v open > /dev/null; then
    # macOS
    (sleep 2 && open "http://localhost:$PORT") &
elif command -v start > /dev/null; then
    # Windows
    (sleep 2 && start "http://localhost:$PORT") &
fi

# Start the web server
./target/release/normans-quantum-proof-encryption --web --port $PORT
