{"rustc": 15597765236515928571, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 187265481308423917, "path": 10703516787701695270, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/allocator-api2-3d50966576ccee68/dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}