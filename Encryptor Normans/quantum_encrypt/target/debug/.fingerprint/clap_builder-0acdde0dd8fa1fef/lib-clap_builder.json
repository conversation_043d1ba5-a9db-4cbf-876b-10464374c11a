{"rustc": 15597765236515928571, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 2500715894117396885, "deps": [[5820056977320921005, "anstream", false, 3833103418312943135], [9394696648929125047, "anstyle", false, 5346597547199207274], [11166530783118767604, "strsim", false, 6182637021805148436], [11649982696571033535, "clap_lex", false, 12009287198730062284]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-0acdde0dd8fa1fef/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}