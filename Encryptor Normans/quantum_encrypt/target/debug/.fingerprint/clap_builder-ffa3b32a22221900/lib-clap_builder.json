{"rustc": 15597765236515928571, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 2500715894117396885, "deps": [[5820056977320921005, "anstream", false, 9274849921400950015], [9394696648929125047, "anstyle", false, 8053117261445057915], [11166530783118767604, "strsim", false, 16466919046197244434], [11649982696571033535, "clap_lex", false, 11003354436507908466]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-ffa3b32a22221900/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}