{"rustc": 15597765236515928571, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 9803944407558555576, "deps": [[3060637413840920116, "proc_macro2", false, 14215205247776230370], [4974441333307933176, "syn", false, 14120733684401936142], [17685210698997651194, "convert_case", false, 8236245397051556717], [17990358020177143287, "quote", false, 776078861414959568]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-impl-0e93aa4d4a466d6a/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}