{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"any_zlib\", \"libz-rs-sys\", \"zlib-rs\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 4662842413980259252, "deps": [[5150019917558475664, "libz_rs_sys", false, 17778947741167347210], [5466618496199522463, "crc32fast", false, 14438777297886201547]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-35c109a3cf2ab676/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}