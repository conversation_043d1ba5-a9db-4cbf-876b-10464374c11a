{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 4662842413980259252, "deps": [[5466618496199522463, "crc32fast", false, 16425063748611913860], [7636735136738807108, "miniz_oxide", false, 11171472759195537660]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-c48e8559bdb588c2/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}