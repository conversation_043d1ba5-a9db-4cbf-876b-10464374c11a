{"rustc": 15597765236515928571, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 15616140867858303385, "deps": [[5230392855116717286, "equivalent", false, 1280986642452673416], [9150530836556604396, "allocator_api2", false, 6746200483633577209], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 2947750166047855944]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-54740d6edfc677d7/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}