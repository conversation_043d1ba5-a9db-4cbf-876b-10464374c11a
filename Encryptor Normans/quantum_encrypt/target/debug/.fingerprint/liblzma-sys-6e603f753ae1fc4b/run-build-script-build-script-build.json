{"rustc": 15597765236515928571, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7676925992031350087, "build_script_build", false, 9733969573432441757]], "local": [{"RerunIfChanged": {"output": "debug/build/liblzma-sys-6e603f753ae1fc4b/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "LZMA_API_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-gnu", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}