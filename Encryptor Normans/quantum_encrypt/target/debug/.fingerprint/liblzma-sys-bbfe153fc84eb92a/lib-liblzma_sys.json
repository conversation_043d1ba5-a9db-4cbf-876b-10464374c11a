{"rustc": 15597765236515928571, "features": "[\"bindgen\"]", "declared_features": "[\"bindgen\", \"default\", \"fat-lto\", \"parallel\", \"static\", \"thin-lto\", \"uncheck_liblzma_version\", \"wasm\"]", "target": 10591174828073752297, "profile": 2241668132362809309, "path": 12329840052985282454, "deps": [[4684437522915235464, "libc", false, 5153603195105344502], [7676925992031350087, "build_script_build", false, 11755903198331749216]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/liblzma-sys-bbfe153fc84eb92a/dep-lib-liblzma_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}