{"rustc": 15597765236515928571, "features": "[\"bindgen\"]", "declared_features": "[\"bindgen\", \"default\", \"fat-lto\", \"parallel\", \"static\", \"thin-lto\", \"uncheck_liblzma_version\", \"wasm\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 15738578236918920991, "deps": [[3214373357989284387, "pkg_config", false, 14874001548067733977], [10689043378672162905, "cc", false, 18193009465690533870]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/liblzma-sys-d093faa6bf3fc064/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}