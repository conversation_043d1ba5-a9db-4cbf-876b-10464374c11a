{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 2241668132362809309, "path": 17460123575799325418, "deps": [[2828590642173593838, "cfg_if", false, 6314316639482672558], [3666196340704888985, "smallvec", false, 13267430705271500278], [4269498962362888130, "build_script_build", false, 980886040712548351], [4684437522915235464, "libc", false, 5153603195105344502]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-bc828445e1028446/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}