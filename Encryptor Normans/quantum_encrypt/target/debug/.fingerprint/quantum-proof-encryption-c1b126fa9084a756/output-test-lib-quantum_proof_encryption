{"$message_type":"diagnostic","message":"failed to resolve: use of undeclared type `TempDir`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/key_management.rs","byte_start":10491,"byte_end":10498,"line_start":283,"line_end":283,"column_start":24,"column_end":31,"is_primary":true,"text":[{"text":"        let temp_dir = TempDir::new().unwrap();","highlight_start":24,"highlight_end":31}],"label":"use of undeclared type `TempDir`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src/key_management.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::errors::{EncryptionError, Result};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tempfile::TempDir;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of undeclared type `TempDir`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:283:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let temp_dir = TempDir::new().unwrap();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of undeclared type `TempDir`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use tempfile::TempDir;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find function `set_modified` in module `fs`","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"src/streaming.rs","byte_start":14496,"byte_end":14508,"line_start":389,"line_end":389,"column_start":21,"column_end":33,"is_primary":true,"text":[{"text":"        let _ = fs::set_modified(output_path, mtime);","highlight_start":21,"highlight_end":33}],"label":"not found in `fs`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m\u001b[1m: cannot find function `set_modified` in module `fs`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:389:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m389\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let _ = fs::set_modified(output_path, mtime);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `fs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find function `set_modified` in module `fs`","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"src/encryption.rs","byte_start":6404,"byte_end":6416,"line_start":166,"line_end":166,"column_start":29,"column_end":41,"is_primary":true,"text":[{"text":"                let _ = fs::set_modified(output_path, mtime);","highlight_start":29,"highlight_end":41}],"label":"not found in `fs`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m\u001b[1m: cannot find function `set_modified` in module `fs`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:166:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let _ = fs::set_modified(output_path, mtime);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `fs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/models.rs","byte_start":70,"byte_end":95,"line_start":3,"line_end":3,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/models.rs","byte_start":66,"byte_end":97,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use zeroize::{Zeroize, ZeroizeOnDrop};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/models.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `EncryptionError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/utils.rs","byte_start":20,"byte_end":35,"line_start":1,"line_end":1,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"use crate::errors::{EncryptionError, Result};","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/utils.rs","byte_start":20,"byte_end":37,"line_start":1,"line_end":1,"column_start":21,"column_end":38,"is_primary":true,"text":[{"text":"use crate::errors::{EncryptionError, Result};","highlight_start":21,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/utils.rs","byte_start":19,"byte_end":20,"line_start":1,"line_end":1,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::errors::{EncryptionError, Result};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/utils.rs","byte_start":43,"byte_end":44,"line_start":1,"line_end":1,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use crate::errors::{EncryptionError, Result};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `EncryptionError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils.rs:1:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::errors::{EncryptionError, Result};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::models::SecureBytes`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/utils.rs","byte_start":50,"byte_end":76,"line_start":2,"line_end":2,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"use crate::models::SecureBytes;","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/utils.rs","byte_start":46,"byte_end":78,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::models::SecureBytes;","highlight_start":1,"highlight_end":32},{"text":"use base64::{engine::general_purpose, Engine as _};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::models::SecureBytes`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::SecureBytes;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `distributions::Alphanumeric`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/utils.rs","byte_start":141,"byte_end":168,"line_start":4,"line_end":4,"column_start":12,"column_end":39,"is_primary":true,"text":[{"text":"use rand::{distributions::Alphanumeric, Rng};","highlight_start":12,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/utils.rs","byte_start":141,"byte_end":170,"line_start":4,"line_end":4,"column_start":12,"column_end":41,"is_primary":true,"text":[{"text":"use rand::{distributions::Alphanumeric, Rng};","highlight_start":12,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/utils.rs","byte_start":140,"byte_end":141,"line_start":4,"line_end":4,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use rand::{distributions::Alphanumeric, Rng};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/utils.rs","byte_start":173,"byte_end":174,"line_start":4,"line_end":4,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use rand::{distributions::Alphanumeric, Rng};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `distributions::Alphanumeric`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils.rs:4:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{distributions::Alphanumeric, Rng};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/key_management.rs","byte_start":10106,"byte_end":10114,"line_start":268,"line_end":268,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/key_management.rs","byte_start":10102,"byte_end":10115,"line_start":268,"line_end":268,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    use super::*;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `super::*`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:268:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use super::*;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tempfile::TempDir`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/key_management.rs","byte_start":10124,"byte_end":10141,"line_start":269,"line_end":269,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    use tempfile::TempDir;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/key_management.rs","byte_start":10120,"byte_end":10142,"line_start":269,"line_end":269,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"    use tempfile::TempDir;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tempfile::TempDir`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:269:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m269\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use tempfile::TempDir;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Aead`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":189,"byte_end":193,"line_start":5,"line_end":5,"column_start":12,"column_end":16,"is_primary":true,"text":[{"text":"    aead::{Aead, AeadInPlace, KeyInit},","highlight_start":12,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":189,"byte_end":195,"line_start":5,"line_end":5,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    aead::{Aead, AeadInPlace, KeyInit},","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Aead`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:5:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    aead::{Aead, AeadInPlace, KeyInit},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":446,"byte_end":450,"line_start":14,"line_end":14,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::{self, BufReader, BufWriter, Read, Seek, SeekFrom, Write};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":446,"byte_end":452,"line_start":14,"line_end":14,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"use std::io::{self, BufReader, BufWriter, Read, Seek, SeekFrom, Write};","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:14:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{self, BufReader, BufWriter, Read, Seek, SeekFrom, Write};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `generate_random_bytes`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/encryption.rs","byte_start":254,"byte_end":275,"line_start":5,"line_end":5,"column_start":50,"column_end":71,"is_primary":true,"text":[{"text":"use crate::utils::{decode_base64, encode_base64, generate_random_bytes, get_file_permissions};","highlight_start":50,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/encryption.rs","byte_start":252,"byte_end":275,"line_start":5,"line_end":5,"column_start":48,"column_end":71,"is_primary":true,"text":[{"text":"use crate::utils::{decode_base64, encode_base64, generate_random_bytes, get_file_permissions};","highlight_start":48,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `generate_random_bytes`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:5:50\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::{decode_base64, encode_base64, generate_random_bytes, get_file_permissions};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Read` and `Write`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/encryption.rs","byte_start":443,"byte_end":447,"line_start":9,"line_end":9,"column_start":43,"column_end":47,"is_primary":true,"text":[{"text":"use std::io::{self, BufReader, BufWriter, Read, Write};","highlight_start":43,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":449,"byte_end":454,"line_start":9,"line_end":9,"column_start":49,"column_end":54,"is_primary":true,"text":[{"text":"use std::io::{self, BufReader, BufWriter, Read, Write};","highlight_start":49,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/encryption.rs","byte_start":441,"byte_end":454,"line_start":9,"line_end":9,"column_start":41,"column_end":54,"is_primary":true,"text":[{"text":"use std::io::{self, BufReader, BufWriter, Read, Write};","highlight_start":41,"highlight_end":54}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Read` and `Write`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:9:43\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{self, BufReader, BufWriter, Read, Write};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tempfile::TempDir`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/encryption.rs","byte_start":15060,"byte_end":15077,"line_start":422,"line_end":422,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    use tempfile::TempDir;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/encryption.rs","byte_start":15056,"byte_end":15078,"line_start":422,"line_end":422,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"    use tempfile::TempDir;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tempfile::TempDir`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:422:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m422\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use tempfile::TempDir;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Tabs` and `Text`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":492,"byte_end":496,"line_start":11,"line_end":11,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"    text::{Line, Span, Text},","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":668,"byte_end":672,"line_start":14,"line_end":14,"column_start":58,"column_end":62,"is_primary":true,"text":[{"text":"        Scrollbar, ScrollbarOrientation, ScrollbarState, Tabs, Wrap,","highlight_start":58,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":490,"byte_end":496,"line_start":11,"line_end":11,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"    text::{Line, Span, Text},","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":666,"byte_end":672,"line_start":14,"line_end":14,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"        Scrollbar, ScrollbarOrientation, ScrollbarState, Tabs, Wrap,","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Tabs` and `Text`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:11:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    text::{Line, Span, Text},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Scrollbar, ScrollbarOrientation, ScrollbarState, Tabs, Wrap,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `fs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":744,"byte_end":746,"line_start":20,"line_end":20,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    fs,","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":738,"byte_end":746,"line_start":19,"line_end":20,"column_start":17,"column_end":7,"is_primary":true,"text":[{"text":"    error::Error,","highlight_start":17,"highlight_end":18},{"text":"    fs,","highlight_start":1,"highlight_end":7}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `fs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fs,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/lib.rs","byte_start":2374,"byte_end":2389,"line_start":78,"line_end":78,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    use std::path::Path;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/lib.rs","byte_start":2370,"byte_end":2390,"line_start":78,"line_end":78,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    use std::path::Path;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::path::Path`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/lib.rs:78:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use std::path::Path;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `as_dyn_error` exists for reference `&Error`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/errors.rs","byte_start":1213,"byte_end":1217,"line_start":48,"line_end":48,"column_start":14,"column_end":18,"is_primary":true,"text":[{"text":"    Argon2(#[from] argon2::Error),","highlight_start":14,"highlight_end":18}],"label":"method cannot be called on `&Error` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/argon2-0.5.3/src/error.rs","byte_start":302,"byte_end":316,"line_start":13,"line_end":13,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"pub enum Error {","highlight_start":1,"highlight_end":15}],"label":"doesn't satisfy `argon2::Error: AsDynError<'_>` or `argon2::Error: StdError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following trait bounds were not satisfied:\n`argon2::Error: StdError`\nwhich is required by `argon2::Error: AsDynError<'_>`\n`&argon2::Error: StdError`\nwhich is required by `&argon2::Error: AsDynError<'_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `as_dyn_error` exists for reference `&Error`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/errors.rs:48:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Argon2(#[from] argon2::Error),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `&Error` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/argon2-0.5.3/src/error.rs:13:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum Error {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `argon2::Error: AsDynError<'_>` or `argon2::Error: StdError`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m           `argon2::Error: StdError`\u001b[0m\n\u001b[0m           which is required by `argon2::Error: AsDynError<'_>`\u001b[0m\n\u001b[0m           `&argon2::Error: StdError`\u001b[0m\n\u001b[0m           which is required by `&argon2::Error: AsDynError<'_>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/key_management.rs","byte_start":919,"byte_end":927,"line_start":33,"line_end":33,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":21,"highlight_end":29}],"label":"expected `Result<Keypair, KyberError>`, found `(_, _)`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/key_management.rs","byte_start":930,"byte_end":947,"line_start":33,"line_end":33,"column_start":32,"column_end":49,"is_primary":false,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":32,"highlight_end":49}],"label":"this expression has type `std::result::Result<Keypair, KyberError>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::result::Result<Keypair, KyberError>`\n  found tuple `(_, _)`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:33:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let (pk, sk) = keypair(&mut rng);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis expression has type `std::result::Result<Keypair, KyberError>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<Keypair, KyberError>`, found `(_, _)`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::result::Result<Keypair, KyberError>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m             found tuple `\u001b[0m\u001b[0m\u001b[1m\u001b[35m(_, _)\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/key_management.rs","byte_start":1213,"byte_end":1221,"line_start":39,"line_end":39,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":21,"highlight_end":29}],"label":"expected `Result<Keypair, KyberError>`, found `(_, _)`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/key_management.rs","byte_start":1224,"byte_end":1241,"line_start":39,"line_end":39,"column_start":32,"column_end":49,"is_primary":false,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":32,"highlight_end":49}],"label":"this expression has type `std::result::Result<Keypair, KyberError>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::result::Result<Keypair, KyberError>`\n  found tuple `(_, _)`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:39:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let (pk, sk) = keypair(&mut rng);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis expression has type `std::result::Result<Keypair, KyberError>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<Keypair, KyberError>`, found `(_, _)`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::result::Result<Keypair, KyberError>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m             found tuple `\u001b[0m\u001b[0m\u001b[1m\u001b[35m(_, _)\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/key_management.rs","byte_start":1361,"byte_end":1369,"line_start":43,"line_end":43,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":21,"highlight_end":29}],"label":"expected `Result<Keypair, KyberError>`, found `(_, _)`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/key_management.rs","byte_start":1372,"byte_end":1389,"line_start":43,"line_end":43,"column_start":32,"column_end":49,"is_primary":false,"text":[{"text":"                let (pk, sk) = keypair(&mut rng);","highlight_start":32,"highlight_end":49}],"label":"this expression has type `std::result::Result<Keypair, KyberError>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::result::Result<Keypair, KyberError>`\n  found tuple `(_, _)`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:43:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let (pk, sk) = keypair(&mut rng);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis expression has type `std::result::Result<Keypair, KyberError>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Result<Keypair, KyberError>`, found `(_, _)`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::result::Result<Keypair, KyberError>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m             found tuple `\u001b[0m\u001b[0m\u001b[1m\u001b[35m(_, _)\u001b[0m\u001b[0m`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `models::KeyMetadata` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/key_management.rs","byte_start":2599,"byte_end":2604,"line_start":80,"line_end":80,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"            metadata: metadata.clone(),","highlight_start":32,"highlight_end":37}],"label":"method not found in `KeyMetadata`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/models.rs","byte_start":2484,"byte_end":2506,"line_start":93,"line_end":93,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"pub struct KeyMetadata {","highlight_start":1,"highlight_end":23}],"label":"method `clone` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `clone`, perhaps you need to implement it:\ncandidate #1: `Clone`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `clone` found for struct `models::KeyMetadata` in the current scope\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/key_management.rs:80:32\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            metadata: metadata.clone(),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `KeyMetadata`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0msrc/models.rs:93:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct KeyMetadata {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `clone` not found for this struct\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait defines an item `clone`, perhaps you need to implement it:\u001b[0m\n\u001b[0m           candidate #1: `Clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `zip::read::ZipFile::<'a>::sanitized_name`: by stripping `..`s from the path, the meaning of paths can change.\n                `mangled_name` can be used if this behaviour is desirable","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/encryption.rs","byte_start":10405,"byte_end":10419,"line_start":281,"line_end":281,"column_start":55,"column_end":69,"is_primary":true,"text":[{"text":"                let outpath = output_folder.join(file.sanitized_name());","highlight_start":55,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `zip::read::ZipFile::<'a>::sanitized_name`: by stripping `..`s from the path, the meaning of paths can change.\u001b[0m\n\u001b[0m\u001b[1m                         `mangled_name` can be used if this behaviour is desirable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:281:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let outpath = output_folder.join(file.sanitized_name());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `zip::read::ZipFile::<'a>::sanitized_name`: by stripping `..`s from the path, the meaning of paths can change.\n                `mangled_name` can be used if this behaviour is desirable","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/encryption.rs","byte_start":11475,"byte_end":11489,"line_start":311,"line_end":311,"column_start":55,"column_end":69,"is_primary":true,"text":[{"text":"                let outpath = output_folder.join(file.sanitized_name());","highlight_start":55,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `zip::read::ZipFile::<'a>::sanitized_name`: by stripping `..`s from the path, the meaning of paths can change.\u001b[0m\n\u001b[0m\u001b[1m                         `mangled_name` can be used if this behaviour is desirable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:311:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let outpath = output_folder.join(file.sanitized_name());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `ratatui::Frame::<'_>::size`: use .area() as it's the more correct name","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":5352,"byte_end":5356,"line_start":193,"line_end":193,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"            .split(f.size());","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `ratatui::Frame::<'_>::size`: use .area() as it's the more correct name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:193:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .split(f.size());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Cow<'_, str>: From<&&str>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":8790,"byte_end":8795,"line_start":272,"line_end":272,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"                        Span::styled(title, style),","highlight_start":38,"highlight_end":43}],"label":"the trait `From<&&str>` is not implemented for `Cow<'_, str>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":8777,"byte_end":8789,"line_start":272,"line_end":272,"column_start":25,"column_end":37,"is_primary":false,"text":[{"text":"                        Span::styled(title, style),","highlight_start":25,"highlight_end":37}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"required for `&&str` to implement `Into<Cow<'_, str>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `Span::<'a>::styled`","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/text/span.rs","byte_start":5222,"byte_end":5228,"line_start":165,"line_end":165,"column_start":12,"column_end":18,"is_primary":false,"text":[{"text":"    pub fn styled<T, S>(content: T, style: S) -> Self","highlight_start":12,"highlight_end":18}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/text/span.rs","byte_start":5286,"byte_end":5304,"line_start":167,"line_end":167,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"        T: Into<Cow<'a, str>>,","highlight_start":12,"highlight_end":30}],"label":"required by this bound in `Span::<'a>::styled`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider dereferencing here","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":8790,"byte_end":8790,"line_start":272,"line_end":272,"column_start":38,"column_end":38,"is_primary":true,"text":[{"text":"                        Span::styled(title, style),","highlight_start":38,"highlight_end":38}],"label":null,"suggested_replacement":"*","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Cow<'_, str>: From<&&str>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:272:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        Span::styled(title, style),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<&&str>` is not implemented for `Cow<'_, str>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `&&str` to implement `Into<Cow<'_, str>>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `Span::<'a>::styled`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/text/span.rs:167:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn styled<T, S>(content: T, style: S) -> Self\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m167\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Into<Cow<'a, str>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Span::<'a>::styled`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider dereferencing here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                        Span::styled(\u001b[0m\u001b[0m\u001b[38;5;10m*\u001b[0m\u001b[0mtitle, style),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 1 argument but 0 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":33171,"byte_end":33173,"line_start":1004,"line_end":1004,"column_start":52,"column_end":54,"is_primary":false,"text":[{"text":"                .scroll((self.scroll_state.position() as u16, 0));","highlight_start":52,"highlight_end":54}],"label":"argument #1 of type `usize` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":33163,"byte_end":33171,"line_start":1004,"line_end":1004,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"                .scroll((self.scroll_state.position() as u16, 0));","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs","byte_start":15918,"byte_end":15926,"line_start":437,"line_end":437,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub const fn position(mut self, position: usize) -> Self {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":33171,"byte_end":33173,"line_start":1004,"line_end":1004,"column_start":52,"column_end":54,"is_primary":true,"text":[{"text":"                .scroll((self.scroll_state.position() as u16, 0));","highlight_start":52,"highlight_end":54}],"label":null,"suggested_replacement":"(/* usize */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1004:44\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1004\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .scroll((self.scroll_state.position() as u16, 0));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #1 of type `usize` is missing\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs:437:18\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn position(mut self, position: usize) -> Self {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1004\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                .scroll((self.scroll_state.position(\u001b[0m\u001b[0m\u001b[38;5;10m/* usize */\u001b[0m\u001b[0m) as u16, 0));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":33556,"byte_end":33594,"line_start":1014,"line_end":1014,"column_start":33,"column_end":71,"is_primary":true,"text":[{"text":"                chunks[1].inner(&Margin { vertical: 1, horizontal: 0 }),","highlight_start":33,"highlight_end":71}],"label":"expected `Margin`, found `&Margin`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":33550,"byte_end":33555,"line_start":1014,"line_end":1014,"column_start":27,"column_end":32,"is_primary":false,"text":[{"text":"                chunks[1].inner(&Margin { vertical: 1, horizontal: 0 }),","highlight_start":27,"highlight_end":32}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/layout/rect.rs","byte_start":3986,"byte_end":3991,"line_start":132,"line_end":132,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub const fn inner(self, margin: Margin) -> Self {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider removing the borrow","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":33556,"byte_end":33557,"line_start":1014,"line_end":1014,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"                chunks[1].inner(&Margin { vertical: 1, horizontal: 0 }),","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1014:33\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1014\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                chunks[1].inner(&Margin { vertical: 1, horizontal: 0 }),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Margin`, found `&Margin`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this method are incorrect\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/layout/rect.rs:132:18\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m132\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn inner(self, margin: Margin) -> Self {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider removing the borrow\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1014\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                chunks[1].inner(\u001b[0m\u001b[0m\u001b[38;5;9m&\u001b[0m\u001b[0mMargin { vertical: 1, horizontal: 0 }),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1014\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                chunks[1].inner(Margin { vertical: 1, horizontal: 0 }),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-primitive cast: `ScrollbarState` as `u16`","code":{"code":"E0605","explanation":"An invalid cast was attempted.\n\nErroneous code examples:\n\n```compile_fail,E0605\nlet x = 0u8;\nx as Vec<u8>; // error: non-primitive cast: `u8` as `std::vec::Vec<u8>`\n\n// Another example\n\nlet v = core::ptr::null::<u8>(); // So here, `v` is a `*const u8`.\nv as &u8; // error: non-primitive cast: `*const u8` as `&u8`\n```\n\nOnly primitive types can be cast into each other. Examples:\n\n```\nlet x = 0u8;\nx as u32; // ok!\n\nlet v = core::ptr::null::<u8>();\nv as *const i8; // ok!\n```\n\nFor more information about casts, take a look at the Type cast section in\n[The Reference Book][1].\n\n[1]: https://doc.rust-lang.org/reference/expressions/operator-expr.html#type-cast-expressions\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":33145,"byte_end":33180,"line_start":1004,"line_end":1004,"column_start":26,"column_end":61,"is_primary":true,"text":[{"text":"                .scroll((self.scroll_state.position() as u16, 0));","highlight_start":26,"highlight_end":61}],"label":"an `as` expression can only be used to convert between primitive types or to coerce to a specific trait object","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0605]\u001b[0m\u001b[0m\u001b[1m: non-primitive cast: `ScrollbarState` as `u16`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1004:26\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1004\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .scroll((self.scroll_state.position() as u16, 0));\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9man `as` expression can only be used to convert between primitive types or to coerce to a specific trait object\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Input: From<crossterm::event::KeyEvent>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":41662,"byte_end":41667,"line_start":1254,"line_end":1254,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"                    self.text_input.input(Input::from(","highlight_start":43,"highlight_end":48}],"label":"the trait `From<crossterm::event::KeyEvent>` is not implemented for `Input`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `Input` implements `From<ratatui::crossterm::event::Event>`\n  `Input` implements `From<ratatui::crossterm::event::KeyEvent>`\n  `Input` implements `From<ratatui::crossterm::event::MouseEvent>`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Input: From<crossterm::event::KeyEvent>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1254:43\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.text_input.input(Input::from(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<crossterm::event::KeyEvent>` is not implemented for `Input`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m               `Input` implements `From<ratatui::crossterm::event::Event>`\u001b[0m\n\u001b[0m               `Input` implements `From<ratatui::crossterm::event::KeyEvent>`\u001b[0m\n\u001b[0m               `Input` implements `From<ratatui::crossterm::event::MouseEvent>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 1 argument but 0 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":64198,"byte_end":64200,"line_start":1811,"line_end":1811,"column_start":47,"column_end":49,"is_primary":false,"text":[{"text":"                    self.scroll_state.position().saturating_sub(1)","highlight_start":47,"highlight_end":49}],"label":"argument #1 of type `usize` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":64190,"byte_end":64198,"line_start":1811,"line_end":1811,"column_start":39,"column_end":47,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_sub(1)","highlight_start":39,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs","byte_start":15918,"byte_end":15926,"line_start":437,"line_end":437,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub const fn position(mut self, position: usize) -> Self {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":64198,"byte_end":64200,"line_start":1811,"line_end":1811,"column_start":47,"column_end":49,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_sub(1)","highlight_start":47,"highlight_end":49}],"label":null,"suggested_replacement":"(/* usize */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1811:39\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.scroll_state.position().saturating_sub(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #1 of type `usize` is missing\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs:437:18\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn position(mut self, position: usize) -> Self {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    self.scroll_state.position(\u001b[0m\u001b[0m\u001b[38;5;10m/* usize */\u001b[0m\u001b[0m).saturating_sub(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `saturating_sub` found for struct `ScrollbarState` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":64201,"byte_end":64215,"line_start":1811,"line_end":1811,"column_start":50,"column_end":64,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_sub(1)","highlight_start":50,"highlight_end":64}],"label":"method not found in `ScrollbarState`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `saturating_sub` found for struct `ScrollbarState` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1811:50\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.scroll_state.position().saturating_sub(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ScrollbarState`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 1 argument but 0 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":64393,"byte_end":64395,"line_start":1816,"line_end":1816,"column_start":47,"column_end":49,"is_primary":false,"text":[{"text":"                    self.scroll_state.position().saturating_add(1)","highlight_start":47,"highlight_end":49}],"label":"argument #1 of type `usize` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":64385,"byte_end":64393,"line_start":1816,"line_end":1816,"column_start":39,"column_end":47,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_add(1)","highlight_start":39,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs","byte_start":15918,"byte_end":15926,"line_start":437,"line_end":437,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub const fn position(mut self, position: usize) -> Self {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":64393,"byte_end":64395,"line_start":1816,"line_end":1816,"column_start":47,"column_end":49,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_add(1)","highlight_start":47,"highlight_end":49}],"label":null,"suggested_replacement":"(/* usize */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1816:39\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.scroll_state.position().saturating_add(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #1 of type `usize` is missing\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/ratatui-0.29.0/src/widgets/scrollbar.rs:437:18\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m437\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn position(mut self, position: usize) -> Self {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                    self.scroll_state.position(\u001b[0m\u001b[0m\u001b[38;5;10m/* usize */\u001b[0m\u001b[0m).saturating_add(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `saturating_add` found for struct `ScrollbarState` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":64396,"byte_end":64410,"line_start":1816,"line_end":1816,"column_start":50,"column_end":64,"is_primary":true,"text":[{"text":"                    self.scroll_state.position().saturating_add(1)","highlight_start":50,"highlight_end":64}],"label":"method not found in `ScrollbarState`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `saturating_add` found for struct `ScrollbarState` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:1816:50\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.scroll_state.position().saturating_add(1)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ScrollbarState`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Stylize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":459,"byte_end":466,"line_start":10,"line_end":10,"column_start":37,"column_end":44,"is_primary":true,"text":[{"text":"    style::{Color, Modifier, Style, Stylize},","highlight_start":37,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Stylize`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:10:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    style::{Color, Modifier, Style, Stylize},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":1220,"byte_end":1229,"line_start":43,"line_end":43,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        mut input: R,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":1220,"byte_end":1224,"line_start":43,"line_end":43,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut input: R,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:43:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut input: R,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":1242,"byte_end":1252,"line_start":44,"line_end":44,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":1242,"byte_end":1246,"line_start":44,"line_end":44,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:44:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut output: W,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":2562,"byte_end":2571,"line_start":78,"line_end":78,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        mut input: R,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":2562,"byte_end":2566,"line_start":78,"line_end":78,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut input: R,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:78:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut input: R,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":2584,"byte_end":2594,"line_start":79,"line_end":79,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":2584,"byte_end":2588,"line_start":79,"line_end":79,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:79:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut output: W,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `buffer`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":4998,"byte_end":5004,"line_start":145,"line_end":145,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":4998,"byte_end":5004,"line_start":145,"line_end":145,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":"_buffer","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `buffer`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:145:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_buffer`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `total_read`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":5062,"byte_end":5072,"line_start":146,"line_end":146,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"        let mut total_read = 0u64;","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":5062,"byte_end":5072,"line_start":146,"line_end":146,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"        let mut total_read = 0u64;","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":"_total_read","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `total_read`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:146:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut total_read = 0u64;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_total_read`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `total_size`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":5093,"byte_end":5103,"line_start":147,"line_end":147,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let total_size = header.file_metadata.original_size;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":5093,"byte_end":5103,"line_start":147,"line_end":147,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let total_size = header.file_metadata.original_size;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_total_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `total_size`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:147:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let total_size = header.file_metadata.original_size;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_total_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":4994,"byte_end":5004,"line_start":145,"line_end":145,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":4994,"byte_end":4998,"line_start":145,"line_end":145,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:145:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut buffer = vec![0u8; self.chunk_size + TAG_SIZE];\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":5058,"byte_end":5072,"line_start":146,"line_end":146,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let mut total_read = 0u64;","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":5058,"byte_end":5062,"line_start":146,"line_end":146,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut total_read = 0u64;","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:146:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut total_read = 0u64;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":6009,"byte_end":6019,"line_start":175,"line_end":175,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":6009,"byte_end":6013,"line_start":175,"line_end":175,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:175:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut output: W,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/streaming.rs","byte_start":6442,"byte_end":6452,"line_start":187,"line_end":187,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/streaming.rs","byte_start":6442,"byte_end":6446,"line_start":187,"line_end":187,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        mut output: W,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/streaming.rs:187:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        mut output: W,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"borrow of moved value: `pb`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src/encryption.rs","byte_start":2762,"byte_end":2786,"line_start":80,"line_end":80,"column_start":41,"column_end":65,"is_primary":false,"text":[{"text":"                .with_progress_callback(move |processed, _total| {","highlight_start":41,"highlight_end":65}],"label":"value moved into closure here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":2809,"byte_end":2811,"line_start":81,"line_end":81,"column_start":21,"column_end":23,"is_primary":false,"text":[{"text":"                    pb.set_position(processed);","highlight_start":21,"highlight_end":23}],"label":"variable moved due to use in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":4071,"byte_end":4073,"line_start":108,"line_end":108,"column_start":13,"column_end":15,"is_primary":true,"text":[{"text":"            pb.finish_with_message(\"Encryption complete\");","highlight_start":13,"highlight_end":15}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":2244,"byte_end":2246,"line_start":70,"line_end":70,"column_start":17,"column_end":19,"is_primary":false,"text":[{"text":"            let pb = ProgressBar::new(file_size);","highlight_start":17,"highlight_end":19}],"label":"move occurs because `pb` has type `ProgressBar`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m: borrow of moved value: `pb`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:108:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let pb = ProgressBar::new(file_size);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmove occurs because `pb` has type `ProgressBar`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .with_progress_callback(move |processed, _total| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvalue moved into closure here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m81\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    pb.set_position(processed);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mvariable moved due to use in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            pb.finish_with_message(\"Encryption complete\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime may not live long enough","code":null,"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":26161,"byte_end":26162,"line_start":792,"line_end":792,"column_start":16,"column_end":17,"is_primary":false,"text":[{"text":"        label: &str,","highlight_start":16,"highlight_end":17}],"label":"let's call the lifetime of this reference `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26139,"byte_end":26140,"line_start":791,"line_end":791,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":10}],"label":"let's call the lifetime of this reference `'2`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26560,"byte_end":26801,"line_start":809,"line_end":816,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        Paragraph::new(display_value)","highlight_start":9,"highlight_end":38},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::ALL)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(style)","highlight_start":1,"highlight_end":41},{"text":"                    .title(label),","highlight_start":1,"highlight_end":35},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(style)","highlight_start":1,"highlight_end":26}],"label":"method was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider introducing a named lifetime parameter and update trait if needed","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":26258,"byte_end":26258,"line_start":796,"line_end":796,"column_start":19,"column_end":19,"is_primary":true,"text":[{"text":"    ) -> Paragraph {","highlight_start":19,"highlight_end":19}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26162,"byte_end":26162,"line_start":792,"line_end":792,"column_start":17,"column_end":17,"is_primary":true,"text":[{"text":"        label: &str,","highlight_start":17,"highlight_end":17}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26129,"byte_end":26129,"line_start":790,"line_end":790,"column_start":26,"column_end":26,"is_primary":true,"text":[{"text":"    fn render_input_field(","highlight_start":26,"highlight_end":26}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: lifetime may not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:809:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'2`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m792\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        label: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'1`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m809\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(display_value)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m810\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m812\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::ALL)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(style)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter and update trait if needed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m790\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    fn render_input_field\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m792\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m        label: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mstr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m793\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         value: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m794\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         is_active: bool,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         placeholder: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m796\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    ) -> Paragraph\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime may not live long enough","code":null,"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":26182,"byte_end":26183,"line_start":793,"line_end":793,"column_start":16,"column_end":17,"is_primary":false,"text":[{"text":"        value: &str,","highlight_start":16,"highlight_end":17}],"label":"let's call the lifetime of this reference `'3`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26139,"byte_end":26140,"line_start":791,"line_end":791,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":10}],"label":"let's call the lifetime of this reference `'2`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26560,"byte_end":26801,"line_start":809,"line_end":816,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        Paragraph::new(display_value)","highlight_start":9,"highlight_end":38},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::ALL)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(style)","highlight_start":1,"highlight_end":41},{"text":"                    .title(label),","highlight_start":1,"highlight_end":35},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(style)","highlight_start":1,"highlight_end":26}],"label":"method was supposed to return data with lifetime `'2` but it is returning data with lifetime `'3`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider introducing a named lifetime parameter and update trait if needed","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":26258,"byte_end":26258,"line_start":796,"line_end":796,"column_start":19,"column_end":19,"is_primary":true,"text":[{"text":"    ) -> Paragraph {","highlight_start":19,"highlight_end":19}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26183,"byte_end":26183,"line_start":793,"line_end":793,"column_start":17,"column_end":17,"is_primary":true,"text":[{"text":"        value: &str,","highlight_start":17,"highlight_end":17}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26129,"byte_end":26129,"line_start":790,"line_end":790,"column_start":26,"column_end":26,"is_primary":true,"text":[{"text":"    fn render_input_field(","highlight_start":26,"highlight_end":26}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: lifetime may not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:809:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'2`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m792\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        label: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m793\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        value: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'3`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m809\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(display_value)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m810\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m812\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::ALL)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(style)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod was supposed to return data with lifetime `'2` but it is returning data with lifetime `'3`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter and update trait if needed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m790\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    fn render_input_field\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m792\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         label: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m793\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m        value: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mstr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m794\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         is_active: bool,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         placeholder: &str,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m796\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    ) -> Paragraph\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime may not live long enough","code":null,"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":26234,"byte_end":26235,"line_start":795,"line_end":795,"column_start":22,"column_end":23,"is_primary":false,"text":[{"text":"        placeholder: &str,","highlight_start":22,"highlight_end":23}],"label":"let's call the lifetime of this reference `'4`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26139,"byte_end":26140,"line_start":791,"line_end":791,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        &self,","highlight_start":9,"highlight_end":10}],"label":"let's call the lifetime of this reference `'2`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26560,"byte_end":26801,"line_start":809,"line_end":816,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        Paragraph::new(display_value)","highlight_start":9,"highlight_end":38},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::ALL)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(style)","highlight_start":1,"highlight_end":41},{"text":"                    .title(label),","highlight_start":1,"highlight_end":35},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(style)","highlight_start":1,"highlight_end":26}],"label":"method was supposed to return data with lifetime `'2` but it is returning data with lifetime `'4`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider introducing a named lifetime parameter and update trait if needed","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":26258,"byte_end":26258,"line_start":796,"line_end":796,"column_start":19,"column_end":19,"is_primary":true,"text":[{"text":"    ) -> Paragraph {","highlight_start":19,"highlight_end":19}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26235,"byte_end":26235,"line_start":795,"line_end":795,"column_start":23,"column_end":23,"is_primary":true,"text":[{"text":"        placeholder: &str,","highlight_start":23,"highlight_end":23}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26129,"byte_end":26129,"line_start":790,"line_end":790,"column_start":26,"column_end":26,"is_primary":true,"text":[{"text":"    fn render_input_field(","highlight_start":26,"highlight_end":26}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: lifetime may not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:809:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'2`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        placeholder: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'4`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m809\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(display_value)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m810\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m811\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m812\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::ALL)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m816\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(style)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod was supposed to return data with lifetime `'2` but it is returning data with lifetime `'4`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter and update trait if needed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m790\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    fn render_input_field\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m791\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m794\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         is_active: bool,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m        placeholder: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mstr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m796\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    ) -> Paragraph\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime may not live long enough","code":null,"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":26852,"byte_end":26853,"line_start":819,"line_end":819,"column_start":44,"column_end":45,"is_primary":false,"text":[{"text":"    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {","highlight_start":44,"highlight_end":45}],"label":"let's call the lifetime of this reference `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":26838,"byte_end":26839,"line_start":819,"line_end":819,"column_start":30,"column_end":31,"is_primary":false,"text":[{"text":"    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {","highlight_start":30,"highlight_end":31}],"label":"let's call the lifetime of this reference `'2`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":27200,"byte_end":27441,"line_start":832,"line_end":839,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        Paragraph::new(display_value)","highlight_start":9,"highlight_end":38},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::ALL)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(style)","highlight_start":1,"highlight_end":41},{"text":"                    .title(label),","highlight_start":1,"highlight_end":35},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(style)","highlight_start":1,"highlight_end":26}],"label":"method was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider introducing a named lifetime parameter and update trait if needed","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":26900,"byte_end":26900,"line_start":819,"line_end":819,"column_start":92,"column_end":92,"is_primary":true,"text":[{"text":"    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {","highlight_start":92,"highlight_end":92}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26853,"byte_end":26853,"line_start":819,"line_end":819,"column_start":45,"column_end":45,"is_primary":true,"text":[{"text":"    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {","highlight_start":45,"highlight_end":45}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":26837,"byte_end":26837,"line_start":819,"line_end":819,"column_start":29,"column_end":29,"is_primary":true,"text":[{"text":"    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {","highlight_start":29,"highlight_end":29}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: lifetime may not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:832:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m819\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    fn render_password_field(&self, label: &str, value: &str, is_active: bool) -> Paragraph {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'2`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m832\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(display_value)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m833\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m834\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m835\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::ALL)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m839\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(style)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter and update trait if needed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m819\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    fn render_password_field\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(&self, label: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mstr, value: &str, is_active: bool) -> Paragraph\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot return value referencing temporary value","code":{"code":"E0515","explanation":"A reference to a local variable was returned.\n\nErroneous code example:\n\n```compile_fail,E0515\nfn get_dangling_reference() -> &'static i32 {\n    let x = 0;\n    &x\n}\n```\n\n```compile_fail,E0515\nuse std::slice::Iter;\nfn get_dangling_iterator<'a>() -> Iter<'a, i32> {\n    let v = vec![1, 2, 3];\n    v.iter()\n}\n```\n\nLocal variables, function parameters and temporaries are all dropped before\nthe end of the function body. A returned reference (or struct containing a\nreference) to such a dropped value would immediately be invalid. Therefore\nit is not allowed to return such a reference.\n\nConsider returning a value that takes ownership of local data instead of\nreferencing it:\n\n```\nuse std::vec::IntoIter;\n\nfn get_integer() -> i32 {\n    let x = 0;\n    x\n}\n\nfn get_owned_iterator() -> IntoIter<i32> {\n    let v = vec![1, 2, 3];\n    v.into_iter()\n}\n```\n"},"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":27200,"byte_end":27441,"line_start":832,"line_end":839,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"        Paragraph::new(display_value)","highlight_start":9,"highlight_end":38},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::ALL)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(style)","highlight_start":1,"highlight_end":41},{"text":"                    .title(label),","highlight_start":1,"highlight_end":35},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(style)","highlight_start":1,"highlight_end":26}],"label":"returns a value referencing data owned by the current function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":27154,"byte_end":27179,"line_start":829,"line_end":829,"column_start":14,"column_end":37,"is_primary":false,"text":[{"text":"            &\"•\".repeat(value.len())","highlight_start":14,"highlight_end":37}],"label":"temporary value created here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0515]\u001b[0m\u001b[0m\u001b[1m: cannot return value referencing temporary value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:832:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m829\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            &\"•\".repeat(value.len())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtemporary value created here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m832\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(display_value)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m833\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m834\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m835\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::ALL)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m839\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(style)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mreturns a value referencing data owned by the current function\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"lifetime may not live long enough","code":null,"level":"error","spans":[{"file_name":"src/ui.rs","byte_start":27491,"byte_end":27492,"line_start":842,"line_end":842,"column_start":43,"column_end":44,"is_primary":false,"text":[{"text":"    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {","highlight_start":43,"highlight_end":44}],"label":"let's call the lifetime of this reference `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":27473,"byte_end":27474,"line_start":842,"line_end":842,"column_start":25,"column_end":26,"is_primary":false,"text":[{"text":"    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {","highlight_start":25,"highlight_end":26}],"label":"let's call the lifetime of this reference `'2`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ui.rs","byte_start":27656,"byte_end":27947,"line_start":848,"line_end":855,"column_start":9,"column_end":53,"is_primary":true,"text":[{"text":"        Paragraph::new(text)","highlight_start":9,"highlight_end":29},{"text":"            .block(","highlight_start":1,"highlight_end":20},{"text":"                Block::default()","highlight_start":1,"highlight_end":33},{"text":"                    .borders(Borders::TOP)","highlight_start":1,"highlight_end":43},{"text":"                    .border_style(Style::default().fg(Color::DarkGray))","highlight_start":1,"highlight_end":72},{"text":"                    .title(\"Help\"),","highlight_start":1,"highlight_end":36},{"text":"            )","highlight_start":1,"highlight_end":14},{"text":"            .style(Style::default().fg(Color::Gray))","highlight_start":1,"highlight_end":53}],"label":"method was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider introducing a named lifetime parameter and update trait if needed","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":27510,"byte_end":27510,"line_start":842,"line_end":842,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":27492,"byte_end":27492,"line_start":842,"line_end":842,"column_start":44,"column_end":44,"is_primary":true,"text":[{"text":"    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {","highlight_start":44,"highlight_end":44}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ui.rs","byte_start":27472,"byte_end":27472,"line_start":842,"line_end":842,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: lifetime may not live long enough\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:848:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m842\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    fn render_help_text(&self, items: Vec<&str>) -> Paragraph {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mlet's call the lifetime of this reference `'2`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m848\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Paragraph::new(text)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m849\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .block(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m850\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Block::default()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m851\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    .borders(Borders::TOP)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m855\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .style(Style::default().fg(Color::Gray))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|____________________________________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod was supposed to return data with lifetime `'2` but it is returning data with lifetime `'1`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter and update trait if needed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m842\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    fn render_help_text\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(&self, items: Vec<&\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mstr>) -> Paragraph\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 24 previous errors; 29 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 24 previous errors; 29 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0277, E0308, E0382, E0425, E0433, E0515, E0599, E0605.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0277, E0308, E0382, E0425, E0433, E0515, E0599, E0605.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
