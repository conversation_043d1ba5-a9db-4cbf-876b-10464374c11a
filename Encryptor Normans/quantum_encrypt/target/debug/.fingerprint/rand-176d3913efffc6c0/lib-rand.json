{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 12920875436585592834, "deps": [[1573238666360410412, "rand_chacha", false, 4771806313265279331], [4684437522915235464, "libc", false, 6490983235778994422], [18130209639506977569, "rand_core", false, 17162823176258107743]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-176d3913efffc6c0/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}