{"rustc": 15597765236515928571, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 11665302398822367850, "deps": [[555019317135488525, "regex_automata", false, 256751041980910926], [9408802513701742484, "regex_syntax", false, 13194589482945917863]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-38c031955603c750/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}