{"rustc": 15597765236515928571, "features": "[\"alloc\", \"meta\", \"nfa-pikevm\", \"nfa-thompson\", \"std\", \"syntax\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 15657897354478470176, "path": 11784029019518888137, "deps": [[9408802513701742484, "regex_syntax", false, 13194589482945917863]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-46d9aba03d48931e/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}