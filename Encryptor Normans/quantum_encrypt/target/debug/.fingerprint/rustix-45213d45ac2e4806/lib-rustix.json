{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"fs\", \"std\", \"stdio\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8475749685358207080, "path": 10964234767330819149, "deps": [[7896293946984509699, "bitflags", false, 5272423333449377466], [12053020504183902936, "build_script_build", false, 11858486638720717201], [12846346674781677812, "linux_raw_sys", false, 4371078829210435679]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-45213d45ac2e4806/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}