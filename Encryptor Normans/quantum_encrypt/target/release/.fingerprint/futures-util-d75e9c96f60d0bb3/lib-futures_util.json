{"rustc": 15597765236515928571, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"futures-sink\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 9368338629311981232, "deps": [[1615478164327904835, "pin_utils", false, 18427260244754421151], [1906322745568073236, "pin_project_lite", false, 13657733487175757994], [5451793922601807560, "slab", false, 6056658057819760751], [7013762810557009322, "futures_sink", false, 6852370326720931530], [7620660491849607393, "futures_core", false, 8662880708890593114], [10565019901765856648, "futures_macro", false, 6319903227912577789], [16240732885093539806, "futures_task", false, 4492549544086346368]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-util-d75e9c96f60d0bb3/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}