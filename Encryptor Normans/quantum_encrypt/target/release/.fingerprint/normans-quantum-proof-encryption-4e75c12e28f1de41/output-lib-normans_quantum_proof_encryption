{"$message_type":"diagnostic","message":"this method takes 6 arguments but 5 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/web_server.rs","byte_start":8883,"byte_end":8887,"line_start":253,"line_end":253,"column_start":110,"column_end":114,"is_primary":false,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;","highlight_start":110,"highlight_end":114}],"label":"argument #5 of type `std::option::Option<i32>` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/web_server.rs","byte_start":8794,"byte_end":8808,"line_start":253,"line_end":253,"column_start":21,"column_end":35,"is_primary":true,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;","highlight_start":21,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src/encryption.rs","byte_start":7130,"byte_end":7160,"line_start":191,"line_end":191,"column_start":9,"column_end":39,"is_primary":false,"text":[{"text":"        compression_level: Option<i32>,","highlight_start":9,"highlight_end":39}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":6968,"byte_end":6982,"line_start":185,"line_end":185,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn encrypt_folder(","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/web_server.rs","byte_start":8808,"byte_end":8888,"line_start":253,"line_end":253,"column_start":35,"column_end":115,"is_primary":true,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;","highlight_start":35,"highlight_end":115}],"label":null,"suggested_replacement":"(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, /* std::option::Option<i32> */, true)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 6 arguments but 5 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/web_server.rs:253:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, true)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #5 of type `std::option::Option<i32>` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:185:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn encrypt_folder(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        compression_level: Option<i32>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), Some(&pub_key), None, \u001b[0m\u001b[0m\u001b[38;5;10m/* std::option::Option<i32> */, \u001b[0m\u001b[0mtrue)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                                              \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 6 arguments but 5 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/web_server.rs","byte_start":9124,"byte_end":9128,"line_start":256,"line_end":256,"column_start":111,"column_end":115,"is_primary":false,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;","highlight_start":111,"highlight_end":115}],"label":"argument #5 of type `std::option::Option<i32>` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/web_server.rs","byte_start":9034,"byte_end":9048,"line_start":256,"line_end":256,"column_start":21,"column_end":35,"is_primary":true,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;","highlight_start":21,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src/encryption.rs","byte_start":7130,"byte_end":7160,"line_start":191,"line_end":191,"column_start":9,"column_end":39,"is_primary":false,"text":[{"text":"        compression_level: Option<i32>,","highlight_start":9,"highlight_end":39}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/encryption.rs","byte_start":6968,"byte_end":6982,"line_start":185,"line_end":185,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn encrypt_folder(","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/web_server.rs","byte_start":9048,"byte_end":9129,"line_start":256,"line_end":256,"column_start":35,"column_end":116,"is_primary":true,"text":[{"text":"                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;","highlight_start":35,"highlight_end":116}],"label":null,"suggested_replacement":"(Path::new(&request.input), Path::new(&output_path), None, Some(&password), /* std::option::Option<i32> */, true)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 6 arguments but 5 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/web_server.rs:256:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), true)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #5 of type `std::option::Option<i32>` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:185:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn encrypt_folder(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        compression_level: Option<i32>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                qpe.encrypt_folder(Path::new(&request.input), Path::new(&output_path), None, Some(&password), \u001b[0m\u001b[0m\u001b[38;5;10m/* std::option::Option<i32> */, \u001b[0m\u001b[0mtrue)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                                               \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `save_key` found for struct `QuantumProofEncryption` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/encryption.rs","byte_start":607,"byte_end":640,"line_start":16,"line_end":16,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"pub struct QuantumProofEncryption {","highlight_start":1,"highlight_end":34}],"label":"method `save_key` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/web_server.rs","byte_start":12959,"byte_end":12967,"line_start":334,"line_end":334,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    qpe.save_key(&pub_key, Path::new(&request.public_key_output), None)?;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `save_keypair` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"src/encryption.rs","byte_start":1200,"byte_end":1397,"line_start":37,"line_end":44,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn save_keypair(","highlight_start":5,"highlight_end":25},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        public_key: &[u8],","highlight_start":1,"highlight_end":27},{"text":"        private_key: &[u8],","highlight_start":1,"highlight_end":28},{"text":"        pub_path: &Path,","highlight_start":1,"highlight_end":25},{"text":"        priv_path: &Path,","highlight_start":1,"highlight_end":26},{"text":"        key_password: Option<&str>,","highlight_start":1,"highlight_end":36},{"text":"    ) -> Result<()> {","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `save_key` found for struct `QuantumProofEncryption` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/web_server.rs:334:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    qpe.save_key(&pub_key, Path::new(&request.public_key_output), None)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0msrc/encryption.rs:16:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct QuantumProofEncryption {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `save_key` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `save_keypair` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:37:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn save_keypair(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        public_key: &[u8],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        private_key: &[u8],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        key_password: Option<&str>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|___________________^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `save_key` found for struct `QuantumProofEncryption` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/encryption.rs","byte_start":607,"byte_end":640,"line_start":16,"line_end":16,"column_start":1,"column_end":34,"is_primary":false,"text":[{"text":"pub struct QuantumProofEncryption {","highlight_start":1,"highlight_end":34}],"label":"method `save_key` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/web_server.rs","byte_start":13033,"byte_end":13041,"line_start":335,"line_end":335,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    qpe.save_key(&priv_key, Path::new(&request.private_key_output), request.key_password.as_deref())?;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `save_keypair` with a similar name, but with different arguments","code":null,"level":"help","spans":[{"file_name":"src/encryption.rs","byte_start":1200,"byte_end":1397,"line_start":37,"line_end":44,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn save_keypair(","highlight_start":5,"highlight_end":25},{"text":"        &self,","highlight_start":1,"highlight_end":15},{"text":"        public_key: &[u8],","highlight_start":1,"highlight_end":27},{"text":"        private_key: &[u8],","highlight_start":1,"highlight_end":28},{"text":"        pub_path: &Path,","highlight_start":1,"highlight_end":25},{"text":"        priv_path: &Path,","highlight_start":1,"highlight_end":26},{"text":"        key_password: Option<&str>,","highlight_start":1,"highlight_end":36},{"text":"    ) -> Result<()> {","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `save_key` found for struct `QuantumProofEncryption` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/web_server.rs:335:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    qpe.save_key(&priv_key, Path::new(&request.private_key_output), request.key_password.as_deref())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0msrc/encryption.rs:16:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct QuantumProofEncryption {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `save_key` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `save_keypair` with a similar name, but with different arguments\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/encryption.rs:37:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn save_keypair(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        public_key: &[u8],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        private_key: &[u8],\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        key_password: Option<&str>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m44\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|___________________^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 3 arguments but 1 argument was supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/web_server.rs","byte_start":6187,"byte_end":6195,"line_start":200,"line_end":200,"column_start":51,"column_end":59,"is_primary":false,"text":[{"text":"    let password = crate::generate_secure_password(length);","highlight_start":51,"highlight_end":59}],"label":"two arguments of type `bool` and `bool` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/web_server.rs","byte_start":6156,"byte_end":6187,"line_start":200,"line_end":200,"column_start":20,"column_end":51,"is_primary":true,"text":[{"text":"    let password = crate::generate_secure_password(length);","highlight_start":20,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"src/utils.rs","byte_start":438,"byte_end":459,"line_start":16,"line_end":16,"column_start":48,"column_end":69,"is_primary":false,"text":[{"text":"pub fn generate_secure_password(length: usize, include_symbols: bool, readable: bool) -> String {","highlight_start":48,"highlight_end":69}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/utils.rs","byte_start":461,"byte_end":475,"line_start":16,"line_end":16,"column_start":71,"column_end":85,"is_primary":false,"text":[{"text":"pub fn generate_secure_password(length: usize, include_symbols: bool, readable: bool) -> String {","highlight_start":71,"highlight_end":85}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/utils.rs","byte_start":398,"byte_end":422,"line_start":16,"line_end":16,"column_start":8,"column_end":32,"is_primary":true,"text":[{"text":"pub fn generate_secure_password(length: usize, include_symbols: bool, readable: bool) -> String {","highlight_start":8,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the arguments","code":null,"level":"help","spans":[{"file_name":"src/web_server.rs","byte_start":6187,"byte_end":6195,"line_start":200,"line_end":200,"column_start":51,"column_end":59,"is_primary":true,"text":[{"text":"    let password = crate::generate_secure_password(length);","highlight_start":51,"highlight_end":59}],"label":null,"suggested_replacement":"(length, /* bool */, /* bool */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 3 arguments but 1 argument was supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/web_server.rs:200:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let password = crate::generate_secure_password(length);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtwo arguments of type `bool` and `bool` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils.rs:16:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn generate_secure_password(length: usize, include_symbols: bool, readable: bool) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let password = crate::generate_secure_password(length\u001b[0m\u001b[0m\u001b[38;5;10m, /* bool */, /* bool */\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 5 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 5 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
