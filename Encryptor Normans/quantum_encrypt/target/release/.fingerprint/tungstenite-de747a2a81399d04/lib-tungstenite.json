{"rustc": 15597765236515928571, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 2040997289075261528, "path": 4841189932241300083, "deps": [[99287295355353247, "data_encoding", false, 18377668627983366843], [3150220818285335163, "url", false, 1528301101081348366], [3712811570531045576, "byteorder", false, 18005668922407484179], [4359956005902820838, "utf8", false, 17336079959531285221], [5986029879202738730, "log", false, 18095684150615386929], [6163892036024256188, "httparse", false, 6585519141327882478], [8008191657135824715, "thiserror", false, 2847273265952028115], [9010263965687315507, "http", false, 6990045644336718490], [10724389056617919257, "sha1", false, 3025466350641764289], [13208667028893622512, "rand", false, 5501272118126093816], [16066129441945555748, "bytes", false, 3563039612772942524]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tungstenite-de747a2a81399d04/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}