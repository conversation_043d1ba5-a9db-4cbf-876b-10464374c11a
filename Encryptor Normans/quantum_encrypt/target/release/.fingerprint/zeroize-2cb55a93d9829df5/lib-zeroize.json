{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"derive\", \"zeroize_derive\"]", "declared_features": "[\"aarch64\", \"alloc\", \"default\", \"derive\", \"serde\", \"simd\", \"std\", \"zeroize_derive\"]", "target": 12572013220049634676, "profile": 2040997289075261528, "path": 15669452446825236471, "deps": [[15553062592622223563, "zeroize_derive", false, 10654127521047337001]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zeroize-2cb55a93d9829df5/dep-lib-zeroize", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}