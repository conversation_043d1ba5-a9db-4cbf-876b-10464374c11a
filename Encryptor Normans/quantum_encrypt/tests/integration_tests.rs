use normans_quantum_proof_encryption::{QuantumProofEncryption, SecurityLevel};
use std::fs;
use std::path::Path;
use tempfile::TempDir;

#[test]
fn test_file_encryption_decryption() {
    let temp_dir = TempDir::new().unwrap();
    let input_path = temp_dir.path().join("test.txt");
    let encrypted_path = temp_dir.path().join("test.txt.qpe");
    let decrypted_path = temp_dir.path().join("test.decrypted.txt");
    
    // Create test file
    let test_content = "This is a test file for quantum-proof encryption!";
    fs::write(&input_path, test_content).unwrap();
    
    // Initialize encryption
    let qpe = QuantumProofEncryption::new(SecurityLevel::Standard);
    
    // Generate keypair
    let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
    
    // Encrypt file
    qpe.encrypt_file(&input_path, &encrypted_path, Some(&pub_key), None, false)
        .unwrap();
    
    // Verify encrypted file exists and is different
    assert!(encrypted_path.exists());
    let encrypted_content = fs::read(&encrypted_path).unwrap();
    assert_ne!(encrypted_content, test_content.as_bytes());
    
    // Decrypt file
    qpe.decrypt_file(&encrypted_path, &decrypted_path, Some(&priv_key), None, false)
        .unwrap();
    
    // Verify decrypted content matches
    let decrypted_content = fs::read_to_string(&decrypted_path).unwrap();
    assert_eq!(decrypted_content, test_content);
}

#[test]
fn test_text_encryption_with_password() {
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);
    let password = "super-secret-password";
    
    let plaintext = "This is a secret message!";
    let encrypted = qpe.encrypt_text(plaintext, None, Some(password)).unwrap();
    
    // Encrypted text should be base64
    assert!(encrypted.chars().all(|c| c.is_alphanumeric() || c == '+' || c == '/' || c == '='));
    
    // Decrypt with correct password
    let decrypted = qpe.decrypt_text(&encrypted, None, Some(password)).unwrap();
    assert_eq!(decrypted, plaintext);
    
    // Decrypt with wrong password should fail
    let wrong_result = qpe.decrypt_text(&encrypted, None, Some("wrong-password"));
    assert!(wrong_result.is_err());
}

#[test]
fn test_large_file_streaming() {
    let temp_dir = TempDir::new().unwrap();
    let input_path = temp_dir.path().join("large.bin");
    let encrypted_path = temp_dir.path().join("large.bin.qpe");
    let decrypted_path = temp_dir.path().join("large.decrypted.bin");
    
    // Create a 10MB test file
    let size = 10 * 1024 * 1024;
    let data: Vec<u8> = (0..size).map(|i| (i % 256) as u8).collect();
    fs::write(&input_path, &data).unwrap();
    
    let qpe = QuantumProofEncryption::new(SecurityLevel::Standard);
    let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
    
    // Encrypt large file
    qpe.encrypt_file(&input_path, &encrypted_path, Some(&pub_key), None, false)
        .unwrap();
    
    // Decrypt large file
    qpe.decrypt_file(&encrypted_path, &decrypted_path, Some(&priv_key), None, false)
        .unwrap();
    
    // Verify content matches
    let decrypted_data = fs::read(&decrypted_path).unwrap();
    assert_eq!(decrypted_data, data);
}

#[test]
fn test_folder_encryption() {
    let temp_dir = TempDir::new().unwrap();
    let folder_path = temp_dir.path().join("test_folder");
    let encrypted_path = temp_dir.path().join("test_folder.qpe");
    let decrypted_path = temp_dir.path().join("test_folder_decrypted");
    
    // Create test folder structure
    fs::create_dir(&folder_path).unwrap();
    fs::write(folder_path.join("file1.txt"), "Content of file 1").unwrap();
    fs::write(folder_path.join("file2.txt"), "Content of file 2").unwrap();
    fs::create_dir(folder_path.join("subfolder")).unwrap();
    fs::write(folder_path.join("subfolder/file3.txt"), "Content of file 3").unwrap();
    
    let qpe = QuantumProofEncryption::new(SecurityLevel::High);
    let password = "folder-password";
    
    // Encrypt folder
    qpe.encrypt_folder(&folder_path, &encrypted_path, None, Some(password), Some(6), false)
        .unwrap();
    
    // Decrypt folder
    qpe.decrypt_folder(&encrypted_path, &decrypted_path, None, Some(password), false)
        .unwrap();
    
    // Verify folder structure
    assert!(decrypted_path.join("file1.txt").exists());
    assert!(decrypted_path.join("file2.txt").exists());
    assert!(decrypted_path.join("subfolder/file3.txt").exists());
    
    // Verify content
    let content1 = fs::read_to_string(decrypted_path.join("file1.txt")).unwrap();
    assert_eq!(content1, "Content of file 1");
}

#[test]
fn test_key_persistence() {
    let temp_dir = TempDir::new().unwrap();
    let pub_path = temp_dir.path().join("test.pub");
    let priv_path = temp_dir.path().join("test.key");
    
    let qpe = QuantumProofEncryption::new(SecurityLevel::Maximum);
    let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
    
    // Save keys with password
    let key_password = "key-protection";
    qpe.save_keypair(&pub_key, &priv_key, &pub_path, &priv_path, Some(key_password))
        .unwrap();
    
    // Load keys
    let (loaded_pub, pub_metadata) = qpe.load_key(&pub_path, None).unwrap();
    let (loaded_priv, priv_metadata) = qpe.load_key(&priv_path, Some(key_password)).unwrap();
    
    // Verify keys match
    assert_eq!(loaded_pub, pub_key);
    assert_eq!(loaded_priv, priv_key);
    
    // Verify metadata
    assert_eq!(pub_metadata.security_level, "maximum");
    assert_eq!(pub_metadata.nist_level, 5);
    assert_eq!(priv_metadata.encrypted, Some(true));
    
    // Test encryption/decryption with loaded keys
    let test_text = "Testing key persistence";
    let encrypted = qpe.encrypt_text(test_text, Some(&loaded_pub), None).unwrap();
    let decrypted = qpe.decrypt_text(&encrypted, Some(&loaded_priv), None).unwrap();
    assert_eq!(decrypted, test_text);
}