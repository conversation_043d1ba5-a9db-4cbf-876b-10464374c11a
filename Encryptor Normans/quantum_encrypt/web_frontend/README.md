# 🌐 Normans Quantum-Proof Encryption - Web Frontend

A beautiful, modern web interface for Normans Quantum-Proof Encryption Program, featuring a responsive design with HTML5, CSS3, and JavaScript.

## ✨ Features

### 🔒 **Encryption Operations**
- **File Encryption** - Encrypt individual files with quantum-safe algorithms
- **Folder Encryption** - Compress and encrypt entire directories
- **Text Encryption** - Encrypt text directly in the browser
- **Multiple Security Levels** - Standard, High, and Maximum security options
- **Flexible Authentication** - Public key or password-based encryption

### 🔓 **Decryption Operations**
- **File Decryption** - Decrypt encrypted files
- **Archive Decryption** - Extract and decrypt folder archives
- **Text Decryption** - Decrypt encrypted text strings
- **Enhanced Error Handling** - Clear, specific error messages for troubleshooting

### 🔑 **Key Management**
- **Keypair Generation** - Create quantum-safe public/private key pairs
- **Security Level Selection** - Choose appropriate security for your needs
- **Key Information Display** - View key details and properties
- **Password Protection** - Optional password protection for private keys

### 🛠️ **Tools & Utilities**
- **Secure Password Generator** - Generate cryptographically secure passwords
- **Customizable Length** - 8-128 character passwords
- **Character Type Selection** - Choose uppercase, lowercase, numbers, symbols
- **One-Click Copy** - Easy clipboard integration

## 🎨 **Design Features**

### **Modern UI/UX**
- **Gradient Backgrounds** - Beautiful purple-blue gradients
- **Glass Morphism** - Frosted glass effects with backdrop blur
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Smooth Animations** - Hover effects and transitions
- **Icon Integration** - Font Awesome icons throughout

### **Interactive Elements**
- **Tabbed Interface** - Easy navigation between functions
- **Dynamic Forms** - Forms adapt based on selected options
- **Progress Indicators** - Visual feedback during operations
- **Modal Dialogs** - Clean result and error displays
- **File Upload Areas** - Drag-and-drop style file selection

### **User Experience**
- **Input Validation** - Real-time form validation
- **Clear Feedback** - Success and error messages
- **Helpful Tooltips** - Guidance for security levels and options
- **Keyboard Shortcuts** - Efficient navigation
- **Accessibility** - Screen reader friendly

## 🚀 **Getting Started**

### **Option 1: Web Server Mode (Recommended)**

1. **Build with web server support:**
   ```bash
   cargo build --release --features web-server
   ```

2. **Start the web server:**
   ```bash
   ./target/release/normans-quantum-proof-encryption --web --port 8080
   ```

3. **Open your browser:**
   ```
   http://localhost:8080
   ```

### **Option 2: Static File Serving**

1. **Serve the web_frontend directory with any web server:**
   ```bash
   # Using Python
   cd web_frontend
   python3 -m http.server 8080
   
   # Using Node.js
   npx serve web_frontend
   
   # Using PHP
   cd web_frontend
   php -S localhost:8080
   ```

2. **Note:** Static serving requires manual backend integration

## 🔧 **Configuration**

### **Web Server Options**
- `--web` - Enable web server mode
- `--port <PORT>` - Set server port (default: 8080)

### **Security Levels**
- **Standard** - Fast encryption, suitable for most files
- **High** - Recommended default, good balance of security and speed
- **Maximum** - Highest security, slower but maximum protection

### **Compression Levels** (for folders)
- **1** - Fastest compression
- **3** - Fast compression
- **6** - Balanced (default)
- **9** - Best compression

## 📱 **Responsive Design**

The interface automatically adapts to different screen sizes:

- **Desktop** (1200px+) - Full layout with side-by-side elements
- **Tablet** (768px-1199px) - Stacked layout with optimized spacing
- **Mobile** (< 768px) - Single column layout with touch-friendly controls

## 🔒 **Security Features**

### **Client-Side Security**
- **No sensitive data storage** - Passwords and keys are not stored in browser
- **Secure form handling** - Proper input sanitization
- **HTTPS ready** - Designed for secure connections

### **Backend Integration**
- **RESTful API** - Clean API endpoints for all operations
- **Error handling** - Comprehensive error reporting
- **File validation** - Server-side input validation

## 🎯 **API Endpoints**

When running in web server mode, the following endpoints are available:

- `POST /api/encrypt` - Encrypt files, folders, or text
- `POST /api/decrypt` - Decrypt files, folders, or text
- `POST /api/generate-keys` - Generate new keypairs
- `POST /api/generate-password` - Generate secure passwords

## 🌟 **Browser Compatibility**

- **Chrome** 90+ ✅
- **Firefox** 88+ ✅
- **Safari** 14+ ✅
- **Edge** 90+ ✅

## 📝 **Usage Examples**

### **Encrypting a File**
1. Select "Encrypt" tab
2. Choose "File" operation type
3. Click "Choose File" and select your file
4. Select security level (High recommended)
5. Choose "Public Key" or "Password" method
6. Upload public key or enter password
7. Click "Encrypt"

### **Generating Keys**
1. Select "Keys" tab
2. Choose security level
3. Enter output filenames
4. Optionally set key password
5. Click "Generate Keypair"

### **Creating Secure Passwords**
1. Select "Tools" tab
2. Set desired length (8-128 characters)
3. Choose character types
4. Click "Generate Password"
5. Click copy button to copy to clipboard

## 🔧 **Development**

### **File Structure**
```
web_frontend/
├── index.html      # Main HTML structure
├── styles.css      # CSS styling and responsive design
├── script.js       # JavaScript functionality
└── README.md       # This documentation
```

### **Customization**
- **Colors** - Modify CSS variables in styles.css
- **Layout** - Adjust grid and flexbox layouts
- **Features** - Add new functionality in script.js

## 🎉 **Benefits Over Terminal UI**

- **Visual Appeal** - Beautiful, modern interface
- **Ease of Use** - Point-and-click operation
- **File Management** - Visual file selection
- **Progress Feedback** - Real-time operation status
- **Error Handling** - Clear, helpful error messages
- **Accessibility** - Works with screen readers
- **Cross-Platform** - Runs in any modern browser

## 🚀 **Future Enhancements**

- **Drag & Drop** - File drag-and-drop support
- **Batch Operations** - Multiple file processing
- **Key Backup** - Secure key backup and recovery
- **Themes** - Multiple color themes
- **Internationalization** - Multi-language support

---

**Enjoy the beautiful, secure, and user-friendly web interface for your quantum-proof encryption needs!** 🔒✨
