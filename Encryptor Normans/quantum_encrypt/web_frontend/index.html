<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Normans Quantum-Proof Encryption</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Normans Quantum-Proof Encryption</h1>
                </div>
                <div class="version">v4.0 - Streaming Edition</div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Navigation Tabs -->
            <nav class="nav-tabs">
                <button class="tab-btn active" data-tab="encrypt">
                    <i class="fas fa-lock"></i> Encrypt
                </button>
                <button class="tab-btn" data-tab="decrypt">
                    <i class="fas fa-unlock"></i> Decrypt
                </button>
                <button class="tab-btn" data-tab="keys">
                    <i class="fas fa-key"></i> Keys
                </button>
                <button class="tab-btn" data-tab="tools">
                    <i class="fas fa-tools"></i> Tools
                </button>
            </nav>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Encrypt Tab -->
                <div id="encrypt" class="tab-pane active">
                    <div class="card">
                        <h2><i class="fas fa-lock"></i> Encryption</h2>
                        
                        <!-- Operation Type -->
                        <div class="form-group">
                            <label>Operation Type:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="encrypt-type" value="file" checked>
                                    <span><i class="fas fa-file"></i> File</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="encrypt-type" value="folder">
                                    <span><i class="fas fa-folder"></i> Folder</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="encrypt-type" value="text">
                                    <span><i class="fas fa-font"></i> Text</span>
                                </label>
                            </div>
                        </div>

                        <!-- File/Folder Input -->
                        <div class="form-group" id="file-input-group">
                            <label for="encrypt-input">Select File/Folder:</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="encrypt-input" class="file-input">
                                <button class="file-btn" onclick="document.getElementById('encrypt-input').click()">
                                    <i class="fas fa-upload"></i> Choose File
                                </button>
                                <span class="file-name">No file selected</span>
                            </div>
                        </div>

                        <!-- Text Input -->
                        <div class="form-group hidden" id="text-input-group">
                            <label for="encrypt-text">Text to Encrypt:</label>
                            <textarea id="encrypt-text" placeholder="Enter your text here..." rows="6"></textarea>
                        </div>

                        <!-- Security Level -->
                        <div class="form-group">
                            <label for="security-level">Security Level:</label>
                            <select id="security-level" class="select-input">
                                <option value="standard">Standard - Fast, suitable for most uses</option>
                                <option value="high" selected>High - Recommended default</option>
                                <option value="maximum">Maximum - Highest security, slower</option>
                            </select>
                        </div>

                        <!-- Compression Level (for folders) -->
                        <div class="form-group hidden" id="compression-group">
                            <label for="compression-level">Compression Level:</label>
                            <select id="compression-level" class="select-input">
                                <option value="3">Low - Fast compression (~40-50% reduction)</option>
                                <option value="6" selected>High - Balanced compression (~60-70% reduction)</option>
                                <option value="9">Maximum - Best compression (~70-85% reduction)</option>
                            </select>
                            <small class="form-help">Higher compression takes longer but achieves better file size reduction</small>
                        </div>

                        <!-- Encryption Method -->
                        <div class="form-group">
                            <label>Encryption Method:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="encrypt-method" value="public-key" checked>
                                    <span><i class="fas fa-key"></i> Public Key</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="encrypt-method" value="password">
                                    <span><i class="fas fa-lock"></i> Password</span>
                                </label>
                            </div>
                        </div>

                        <!-- Public Key Input -->
                        <div class="form-group" id="public-key-group">
                            <label for="public-key">Public Key File:</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="public-key" class="file-input" accept=".key,.pub">
                                <button class="file-btn" onclick="document.getElementById('public-key').click()">
                                    <i class="fas fa-key"></i> Choose Key
                                </button>
                                <span class="file-name">No key selected</span>
                            </div>
                        </div>

                        <!-- Password Input -->
                        <div class="form-group hidden" id="password-group">
                            <label for="encrypt-password">Password:</label>
                            <div class="password-wrapper">
                                <input type="password" id="encrypt-password" placeholder="Enter password">
                                <button type="button" class="password-toggle" onclick="togglePassword('encrypt-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <button type="button" class="generate-password-btn" onclick="generatePassword()">
                                <i class="fas fa-random"></i> Generate Secure Password
                            </button>
                        </div>

                        <!-- Output Path -->
                        <div class="form-group">
                            <label for="encrypt-output">Output Path (optional):</label>
                            <input type="text" id="encrypt-output" placeholder="Auto-generated if empty">
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="performEncryption()">
                                <i class="fas fa-lock"></i> Encrypt
                            </button>
                            <button class="btn btn-secondary" onclick="clearForm('encrypt')">
                                <i class="fas fa-eraser"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Decrypt Tab -->
                <div id="decrypt" class="tab-pane">
                    <div class="card">
                        <h2><i class="fas fa-unlock"></i> Decryption</h2>
                        
                        <!-- Operation Type -->
                        <div class="form-group">
                            <label>Operation Type:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="decrypt-type" value="file" checked>
                                    <span><i class="fas fa-file"></i> File</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="decrypt-type" value="folder">
                                    <span><i class="fas fa-folder"></i> Archive</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="decrypt-type" value="text">
                                    <span><i class="fas fa-font"></i> Text</span>
                                </label>
                            </div>
                        </div>

                        <!-- File/Archive Input -->
                        <div class="form-group" id="decrypt-file-input-group">
                            <label for="decrypt-input">Select Encrypted File/Archive:</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="decrypt-input" class="file-input" accept=".qpe">
                                <button class="file-btn" onclick="document.getElementById('decrypt-input').click()">
                                    <i class="fas fa-upload"></i> Choose File
                                </button>
                                <span class="file-name">No file selected</span>
                            </div>
                        </div>

                        <!-- Text Input -->
                        <div class="form-group hidden" id="decrypt-text-input-group">
                            <label for="decrypt-text">Encrypted Text:</label>
                            <textarea id="decrypt-text" placeholder="Paste encrypted text here..." rows="6"></textarea>
                        </div>

                        <!-- Decryption Method -->
                        <div class="form-group">
                            <label>Decryption Method:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="decrypt-method" value="private-key" checked>
                                    <span><i class="fas fa-key"></i> Private Key</span>
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="decrypt-method" value="password">
                                    <span><i class="fas fa-lock"></i> Password</span>
                                </label>
                            </div>
                        </div>

                        <!-- Private Key Input -->
                        <div class="form-group" id="private-key-group">
                            <label for="private-key">Private Key File:</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="private-key" class="file-input" accept=".key">
                                <button class="file-btn" onclick="document.getElementById('private-key').click()">
                                    <i class="fas fa-key"></i> Choose Key
                                </button>
                                <span class="file-name">No key selected</span>
                            </div>
                            
                            <label for="key-password">Key Password (if encrypted):</label>
                            <div class="password-wrapper">
                                <input type="password" id="key-password" placeholder="Enter key password (optional)">
                                <button type="button" class="password-toggle" onclick="togglePassword('key-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Password Input -->
                        <div class="form-group hidden" id="decrypt-password-group">
                            <label for="decrypt-password">Password:</label>
                            <div class="password-wrapper">
                                <input type="password" id="decrypt-password" placeholder="Enter password">
                                <button type="button" class="password-toggle" onclick="togglePassword('decrypt-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Output Path -->
                        <div class="form-group">
                            <label for="decrypt-output">Output Path (optional):</label>
                            <input type="text" id="decrypt-output" placeholder="Auto-generated if empty">
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="performDecryption()">
                                <i class="fas fa-unlock"></i> Decrypt
                            </button>
                            <button class="btn btn-secondary" onclick="clearForm('decrypt')">
                                <i class="fas fa-eraser"></i> Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Keys Tab -->
                <div id="keys" class="tab-pane">
                    <div class="card">
                        <h2><i class="fas fa-key"></i> Key Management</h2>
                        
                        <!-- Generate Keys Section -->
                        <div class="section">
                            <h3><i class="fas fa-plus-circle"></i> Generate New Keypair</h3>
                            
                            <div class="form-group">
                                <label for="key-security-level">Security Level:</label>
                                <select id="key-security-level" class="select-input">
                                    <option value="standard">Standard</option>
                                    <option value="high" selected>High</option>
                                    <option value="maximum">Maximum</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="public-key-output">Public Key Output:</label>
                                <input type="text" id="public-key-output" placeholder="public_key.key">
                            </div>

                            <div class="form-group">
                                <label for="private-key-output">Private Key Output:</label>
                                <input type="text" id="private-key-output" placeholder="private_key.key">
                            </div>

                            <div class="form-group">
                                <label for="key-encryption-password">Key Encryption Password (optional):</label>
                                <div class="password-wrapper">
                                    <input type="password" id="key-encryption-password" placeholder="Leave empty for unencrypted keys">
                                    <button type="button" class="password-toggle" onclick="togglePassword('key-encryption-password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <button class="btn btn-primary" onclick="generateKeys()">
                                <i class="fas fa-key"></i> Generate Keypair
                            </button>
                        </div>

                        <!-- Key Info Section -->
                        <div class="section">
                            <h3><i class="fas fa-info-circle"></i> Key Information</h3>
                            
                            <div class="form-group">
                                <label for="key-info-file">Key File:</label>
                                <div class="file-input-wrapper">
                                    <input type="file" id="key-info-file" class="file-input" accept=".key,.pub">
                                    <button class="file-btn" onclick="document.getElementById('key-info-file').click()">
                                        <i class="fas fa-key"></i> Choose Key
                                    </button>
                                    <span class="file-name">No key selected</span>
                                </div>
                            </div>

                            <button class="btn btn-secondary" onclick="showKeyInfo()">
                                <i class="fas fa-info"></i> Show Key Info
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tools Tab -->
                <div id="tools" class="tab-pane">
                    <div class="card">
                        <h2><i class="fas fa-tools"></i> Tools & Utilities</h2>
                        
                        <!-- Password Generator -->
                        <div class="section">
                            <h3><i class="fas fa-random"></i> Password Generator</h3>
                            
                            <div class="form-group">
                                <label for="password-length">Password Length:</label>
                                <input type="number" id="password-length" value="32" min="8" max="128">
                            </div>

                            <div class="form-group">
                                <label>Character Types:</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="include-uppercase" checked>
                                        <span>Uppercase Letters (A-Z)</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="include-lowercase" checked>
                                        <span>Lowercase Letters (a-z)</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="include-numbers" checked>
                                        <span>Numbers (0-9)</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="include-symbols" checked>
                                        <span>Symbols (!@#$%^&*)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="generated-password">Generated Password:</label>
                                <div class="password-output">
                                    <input type="text" id="generated-password" readonly>
                                    <button type="button" class="copy-btn" onclick="copyToClipboard('generated-password')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <button class="btn btn-primary" onclick="generateSecurePassword()">
                                <i class="fas fa-random"></i> Generate Password
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Enhanced Progress Modal -->
        <div id="progress-modal" class="modal">
            <div class="modal-content">
                <h3 id="progress-title"><i class="fas fa-cog fa-spin"></i> Processing...</h3>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                        <div class="progress-percentage" id="progress-percentage">0%</div>
                    </div>
                    <div class="progress-details">
                        <div class="progress-banner" id="progress-banner">
                            <div class="progress-message">
                                <i class="fas fa-cog fa-spin" style="margin-right: 10px; color: #007bff;"></i>
                                <span id="progress-text">Please wait...</span>
                            </div>
                            <div class="progress-submessage" id="progress-submessage">
                                Processing your request. This may take a few moments.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Result Modal -->
        <div id="result-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="result-title"></h3>
                    <button class="close-btn" onclick="closeModal('result-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p id="result-message"></p>
                    <div id="result-output" class="result-output"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
