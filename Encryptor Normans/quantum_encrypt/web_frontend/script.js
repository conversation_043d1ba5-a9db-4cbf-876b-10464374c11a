// Global variables
let currentTab = 'encrypt';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeFormHandlers();
    initializeFileInputs();
});

// Tab Management
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            switchTab(tabId);
        });
    });
}

function switchTab(tabId) {
    // Update buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // Update panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    document.getElementById(tabId).classList.add('active');

    currentTab = tabId;
}

// Form Handlers
function initializeFormHandlers() {
    // Encrypt type handlers
    document.querySelectorAll('input[name="encrypt-type"]').forEach(radio => {
        radio.addEventListener('change', handleEncryptTypeChange);
    });

    // Decrypt type handlers
    document.querySelectorAll('input[name="decrypt-type"]').forEach(radio => {
        radio.addEventListener('change', handleDecryptTypeChange);
    });

    // Encrypt method handlers
    document.querySelectorAll('input[name="encrypt-method"]').forEach(radio => {
        radio.addEventListener('change', handleEncryptMethodChange);
    });

    // Decrypt method handlers
    document.querySelectorAll('input[name="decrypt-method"]').forEach(radio => {
        radio.addEventListener('change', handleDecryptMethodChange);
    });
}

function handleEncryptTypeChange(event) {
    const type = event.target.value;
    const fileGroup = document.getElementById('file-input-group');
    const textGroup = document.getElementById('text-input-group');
    const compressionGroup = document.getElementById('compression-group');
    const fileInput = document.getElementById('encrypt-input');

    if (type === 'text') {
        fileGroup.classList.add('hidden');
        textGroup.classList.remove('hidden');
        compressionGroup.classList.add('hidden');
    } else {
        fileGroup.classList.remove('hidden');
        textGroup.classList.add('hidden');

        if (type === 'folder') {
            compressionGroup.classList.remove('hidden');
            // Enable directory selection for folders
            fileInput.setAttribute('webkitdirectory', '');
            fileInput.setAttribute('directory', '');
            fileInput.setAttribute('multiple', '');
        } else {
            compressionGroup.classList.add('hidden');
            // Remove directory attributes for files
            fileInput.removeAttribute('webkitdirectory');
            fileInput.removeAttribute('directory');
            fileInput.removeAttribute('multiple');
        }
    }
}

function handleDecryptTypeChange(event) {
    const type = event.target.value;
    const fileGroup = document.getElementById('decrypt-file-input-group');
    const textGroup = document.getElementById('decrypt-text-input-group');

    if (type === 'text') {
        fileGroup.classList.add('hidden');
        textGroup.classList.remove('hidden');
    } else {
        fileGroup.classList.remove('hidden');
        textGroup.classList.add('hidden');
    }
}

function handleEncryptMethodChange(event) {
    const method = event.target.value;
    const publicKeyGroup = document.getElementById('public-key-group');
    const passwordGroup = document.getElementById('password-group');

    if (method === 'password') {
        publicKeyGroup.classList.add('hidden');
        passwordGroup.classList.remove('hidden');
    } else {
        publicKeyGroup.classList.remove('hidden');
        passwordGroup.classList.add('hidden');
    }
}

function handleDecryptMethodChange(event) {
    const method = event.target.value;
    const privateKeyGroup = document.getElementById('private-key-group');
    const passwordGroup = document.getElementById('decrypt-password-group');

    if (method === 'password') {
        privateKeyGroup.classList.add('hidden');
        passwordGroup.classList.remove('hidden');
    } else {
        privateKeyGroup.classList.remove('hidden');
        passwordGroup.classList.add('hidden');
    }
}

// File Input Handlers
function initializeFileInputs() {
    const fileInputs = document.querySelectorAll('.file-input');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            let displayText = 'No file selected';

            if (this.files && this.files.length > 0) {
                if (this.files.length === 1) {
                    displayText = this.files[0].name;
                } else {
                    // Multiple files (folder selection)
                    displayText = `${this.files.length} files selected`;

                    // Try to get folder name from first file path
                    const firstFile = this.files[0];
                    if (firstFile.webkitRelativePath) {
                        const folderName = firstFile.webkitRelativePath.split('/')[0];
                        displayText = `Folder: ${folderName} (${this.files.length} files)`;
                    }
                }
            }

            const fileNameSpan = this.parentElement.querySelector('.file-name');
            if (fileNameSpan) {
                fileNameSpan.textContent = displayText;
            }
        });
    });
}

// Password Utilities
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        button.className = 'fas fa-eye';
    }
}

function generatePassword() {
    const password = generateSecurePasswordString(32);
    document.getElementById('encrypt-password').value = password;
}

async function generateSecurePassword() {
    const length = parseInt(document.getElementById('password-length').value);
    const includeUppercase = document.getElementById('include-uppercase').checked;
    const includeLowercase = document.getElementById('include-lowercase').checked;
    const includeNumbers = document.getElementById('include-numbers').checked;
    const includeSymbols = document.getElementById('include-symbols').checked;

    try {
        const result = await apiRequest('generate-password', {
            length: length,
            include_uppercase: includeUppercase,
            include_lowercase: includeLowercase,
            include_numbers: includeNumbers,
            include_symbols: includeSymbols
        });

        document.getElementById('generated-password').value = result.password;
    } catch (error) {
        // Fallback to client-side generation
        console.log('Using client-side password generation:', error.message);
        const password = generateSecurePasswordString(length, {
            uppercase: includeUppercase,
            lowercase: includeLowercase,
            numbers: includeNumbers,
            symbols: includeSymbols
        });

        document.getElementById('generated-password').value = password;

        // Show a subtle notification that this is client-side generation
        if (error.message.includes('Demo Mode')) {
            showInfo('Password Generated', 'Password generated using client-side cryptographically secure random generator. For server-side generation, start the web server.');
        }
    }
}

function generateSecurePasswordString(length, options = {}) {
    const defaults = {
        uppercase: true,
        lowercase: true,
        numbers: true,
        symbols: true
    };
    
    const opts = { ...defaults, ...options };
    
    let charset = '';
    if (opts.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (opts.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (opts.numbers) charset += '0123456789';
    if (opts.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    if (charset === '') {
        showError('Please select at least one character type');
        return '';
    }
    
    let password = '';
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        showSuccess('Copied to clipboard!');
    } catch (err) {
        showError('Failed to copy to clipboard');
    }
}

// Main Operations
async function performEncryption() {
    const type = document.querySelector('input[name="encrypt-type"]:checked').value;
    const method = document.querySelector('input[name="encrypt-method"]:checked').value;

    // Validate inputs
    if (!validateEncryptionInputs(type, method)) {
        return;
    }

    // Execute encryption
    showProgress('Encrypting...', 'Preparing encryption...');

    try {
        let result;

        if (type === 'text') {
            // For text, use JSON API
            const requestData = buildEncryptionRequest(type, method);
            result = await apiRequest('encrypt', requestData);
        } else {
            // For files/folders, use file upload API
            result = await uploadAndEncrypt(type, method);
        }

        hideProgress();
        showEncryptionResult(result);
    } catch (error) {
        hideProgress();
        showResult('Encryption Failed', error.message, 'error');
    }
}

async function performDecryption() {
    const type = document.querySelector('input[name="decrypt-type"]:checked').value;
    const method = document.querySelector('input[name="decrypt-method"]:checked').value;

    // Validate inputs
    if (!validateDecryptionInputs(type, method)) {
        return;
    }

    // Build request data
    const requestData = buildDecryptionRequest(type, method);

    // Execute decryption
    showProgress('Decrypting...', 'Preparing decryption...');

    try {
        const result = await apiRequest('decrypt', requestData);
        hideProgress();
        showResult('Decryption Successful', result.output, 'success');
    } catch (error) {
        hideProgress();
        showResult('Decryption Failed', error.message, 'error');
    }
}

async function generateKeys() {
    const securityLevel = document.getElementById('key-security-level').value;
    const publicKeyOutput = document.getElementById('public-key-output').value || 'public_key.key';
    const privateKeyOutput = document.getElementById('private-key-output').value || 'private_key.key';
    const keyPassword = document.getElementById('key-encryption-password').value;

    const requestData = {
        security_level: securityLevel,
        public_key_output: publicKeyOutput,
        private_key_output: privateKeyOutput,
        key_password: keyPassword || null
    };

    showProgress('Generating Keys...', 'Creating quantum-safe keypair...');

    try {
        const result = await apiRequest('generate-keys', requestData);
        hideProgress();
        showResult('Keys Generated Successfully', result.output, 'success');
    } catch (error) {
        hideProgress();
        showResult('Key Generation Failed', error.message, 'error');
    }
}

async function showKeyInfo() {
    const keyFile = document.getElementById('key-info-file').files[0];
    
    if (!keyFile) {
        showError('Please select a key file');
        return;
    }
    
    // This would need to be implemented to read key file info
    showInfo('Key Information', 'Key information feature would be implemented here');
}

// Request Building Functions
function buildEncryptionRequest(type, method) {
    const requestData = {
        operation_type: type,
        method: method,
        security_level: document.getElementById('security-level').value
    };

    // Add input data
    if (type === 'text') {
        requestData.input = document.getElementById('encrypt-text').value;
    } else {
        const fileInput = document.getElementById('encrypt-input');
        if (fileInput.files && fileInput.files.length > 0) {
            if (type === 'folder') {
                // For folders, use the folder name from the first file's path
                const firstFile = fileInput.files[0];
                if (firstFile.webkitRelativePath) {
                    requestData.input = firstFile.webkitRelativePath.split('/')[0];
                } else {
                    requestData.input = 'selected_folder';
                }
            } else {
                // For files, use the file name
                requestData.input = fileInput.files[0].name;
            }
        }
    }

    // Add compression level for folders
    if (type === 'folder') {
        requestData.compression_level = parseInt(document.getElementById('compression-level').value);
    }

    // Add encryption method details
    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        if (publicKey) {
            requestData.public_key_path = publicKey.name;
        }
    } else {
        requestData.password = document.getElementById('encrypt-password').value;
    }

    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        requestData.output_path = output;
    }

    return requestData;
}

function buildDecryptionRequest(type, method) {
    const requestData = {
        operation_type: type,
        method: method
    };

    // Add input data
    if (type === 'text') {
        requestData.input = document.getElementById('decrypt-text').value;
    } else {
        const fileInput = document.getElementById('decrypt-input').files[0];
        requestData.input = fileInput.name; // Note: In real implementation, file would be uploaded
    }

    // Add decryption method details
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        requestData.private_key_path = privateKey.name; // Note: In real implementation, file would be uploaded

        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            requestData.key_password = keyPassword;
        }
    } else {
        requestData.password = document.getElementById('decrypt-password').value;
    }

    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        requestData.output_path = output;
    }

    return requestData;
}

// Legacy Command Building Functions (for reference)
function buildEncryptionCommand(type, method) {
    const command = ['./target/release/normans-quantum-proof-encryption'];
    
    // Add operation type
    if (type === 'file') {
        command.push('encrypt-file');
        const fileInput = document.getElementById('encrypt-input').files[0];
        command.push(fileInput.name); // Note: This is simplified - would need proper file handling
    } else if (type === 'folder') {
        command.push('encrypt-folder');
        const folderInput = document.getElementById('encrypt-input').files[0];
        command.push(folderInput.name);
    } else if (type === 'text') {
        command.push('encrypt-text');
        const text = document.getElementById('encrypt-text').value;
        command.push(`"${text}"`);
    }
    
    // Add security level
    const securityLevel = document.getElementById('security-level').value;
    command.push('--security', securityLevel);
    
    // Add encryption method
    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        command.push('--public-key', publicKey.name);
    } else {
        const password = document.getElementById('encrypt-password').value;
        command.push('--password');
        // Password would be provided via stdin
    }
    
    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        command.push('--output', output);
    }
    
    return command;
}

function buildDecryptionCommand(type, method) {
    const command = ['./target/release/normans-quantum-proof-encryption'];
    
    // Add operation type
    if (type === 'file') {
        command.push('decrypt-file');
        const fileInput = document.getElementById('decrypt-input').files[0];
        command.push(fileInput.name);
    } else if (type === 'folder') {
        command.push('decrypt-folder');
        const folderInput = document.getElementById('decrypt-input').files[0];
        command.push(folderInput.name);
    } else if (type === 'text') {
        command.push('decrypt-text');
        const text = document.getElementById('decrypt-text').value;
        command.push(`"${text}"`);
    }
    
    // Add decryption method
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        command.push('--private-key', privateKey.name);
        
        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            command.push('--key-password', keyPassword);
        }
    } else {
        const password = document.getElementById('decrypt-password').value;
        command.push('--password');
        // Password would be provided via stdin
    }
    
    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        command.push('--output', output);
    }
    
    return command;
}

// Validation Functions
function validateEncryptionInputs(type, method) {
    if (type === 'text') {
        const text = document.getElementById('encrypt-text').value;
        if (!text.trim()) {
            showError('Please enter text to encrypt');
            return false;
        }
    } else {
        const fileInput = document.getElementById('encrypt-input');
        if (!fileInput.files || fileInput.files.length === 0) {
            const itemType = type === 'folder' ? 'folder' : 'file';
            showError(`Please select a ${itemType} to encrypt`);
            return false;
        }
    }

    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        if (!publicKey) {
            showError('Please select a public key file');
            return false;
        }
    } else {
        const password = document.getElementById('encrypt-password').value;
        if (!password) {
            showError('Please enter a password');
            return false;
        }
    }

    return true;
}

function validateDecryptionInputs(type, method) {
    if (type === 'text') {
        const text = document.getElementById('decrypt-text').value;
        if (!text.trim()) {
            showError('Please enter encrypted text');
            return false;
        }
    } else {
        const fileInput = document.getElementById('decrypt-input').files[0];
        if (!fileInput) {
            showError('Please select an encrypted file or archive');
            return false;
        }
    }
    
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        if (!privateKey) {
            showError('Please select a private key file');
            return false;
        }
    } else {
        const password = document.getElementById('decrypt-password').value;
        if (!password) {
            showError('Please enter a password');
            return false;
        }
    }
    
    return true;
}

// File Upload and Encryption
async function uploadAndEncrypt(type, method) {
    const formData = new FormData();

    // Add form fields
    formData.append('operation_type', type);
    formData.append('method', method);
    formData.append('security_level', document.getElementById('security-level').value);

    // Add compression level for folders
    if (type === 'folder') {
        formData.append('compression_level', document.getElementById('compression-level').value);
    }

    // Add files
    const fileInput = document.getElementById('encrypt-input');
    if (fileInput.files && fileInput.files.length > 0) {
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('file', fileInput.files[i]);
        }
    }

    // Add encryption method details
    if (method === 'public-key') {
        const publicKeyInput = document.getElementById('public-key');
        if (publicKeyInput.files && publicKeyInput.files[0]) {
            formData.append('public_key', publicKeyInput.files[0]);
        }
    } else {
        formData.append('password', document.getElementById('encrypt-password').value);
    }

    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        formData.append('output_path', output);
    }

    // Upload and encrypt
    try {
        const response = await fetch('/api/encrypt-upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: File upload requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8080');
        }
        throw new Error(`Upload failed: ${error.message}`);
    }
}

// API Communication
async function apiRequest(endpoint, data) {
    try {
        const response = await fetch(`/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: This is a demonstration of the web interface. To enable full functionality, start the program with: ./target/release/normans-quantum-proof-encryption --web --port 8080');
        }
        throw new Error(`API request failed: ${error.message}`);
    }
}

// UI Helper Functions
function showProgress(title, message) {
    document.getElementById('progress-modal').classList.add('show');
    document.querySelector('#progress-modal h3').innerHTML = `<i class="fas fa-cog fa-spin"></i> ${title}`;
    document.getElementById('progress-text').textContent = message;
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;
        document.getElementById('progress-fill').style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
        }
    }, 200);
}

function hideProgress() {
    document.getElementById('progress-modal').classList.remove('show');
    document.getElementById('progress-fill').style.width = '0%';
}

function showResult(title, message, type) {
    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');
    
    titleElement.innerHTML = type === 'success' 
        ? `<i class="fas fa-check-circle" style="color: #28a745;"></i> ${title}`
        : `<i class="fas fa-exclamation-circle" style="color: #dc3545;"></i> ${title}`;
    
    messageElement.textContent = message;
    outputElement.textContent = message;
    
    modal.classList.add('show');
}

function showError(message) {
    showResult('Error', message, 'error');
}

function showSuccess(message) {
    showResult('Success', message, 'success');
}

function showInfo(title, message) {
    showResult(title, message, 'info');
}

function showEncryptionResult(result) {
    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');

    titleElement.innerHTML = `<i class="fas fa-check-circle" style="color: #28a745;"></i> Encryption Successful`;

    // Create detailed result message
    let message = `${result.message}\n\n`;
    message += `📁 Files processed: ${result.original_files}\n`;
    message += `📊 Original size: ${formatBytes(result.original_size)}\n`;
    message += `🔒 Encrypted size: ${formatBytes(result.encrypted_size)}\n`;
    message += `📉 Compression: ${result.compression_ratio}\n`;
    message += `📦 ${result.compression_info}\n\n`;
    message += `📄 Output file: ${result.output_filename}`;

    messageElement.textContent = message;

    // Create download section
    const downloadSection = document.createElement('div');
    downloadSection.className = 'download-section';
    downloadSection.innerHTML = `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">
                <i class="fas fa-download"></i> Download Encrypted File
            </h4>
            <p style="margin: 0 0 15px 0; color: #666;">
                Your encrypted file is ready for download. Click the button below to save it locally.
            </p>
            <button class="btn btn-primary" onclick="downloadFile('${result.download_id}', '${result.output_filename}')">
                <i class="fas fa-download"></i> Download ${result.output_filename}
            </button>
            <p style="margin: 15px 0 0 0; font-size: 0.9em; color: #666;">
                <i class="fas fa-clock"></i> Download link expires in 1 hour
            </p>
        </div>
    `;

    outputElement.innerHTML = '';
    outputElement.appendChild(downloadSection);

    modal.classList.add('show');
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function downloadFile(downloadId, filename) {
    try {
        const response = await fetch(`/api/download/${downloadId}`);

        if (!response.ok) {
            throw new Error(`Download failed: ${response.statusText}`);
        }

        const blob = await response.blob();

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showSuccess('File downloaded successfully!');
    } catch (error) {
        showError(`Download failed: ${error.message}`);
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

function clearForm(formType) {
    if (formType === 'encrypt') {
        document.getElementById('encrypt-input').value = '';
        document.getElementById('encrypt-text').value = '';
        document.getElementById('public-key').value = '';
        document.getElementById('encrypt-password').value = '';
        document.getElementById('encrypt-output').value = '';
        
        // Reset file name displays
        document.querySelectorAll('#encrypt .file-name').forEach(span => {
            span.textContent = 'No file selected';
        });
    } else if (formType === 'decrypt') {
        document.getElementById('decrypt-input').value = '';
        document.getElementById('decrypt-text').value = '';
        document.getElementById('private-key').value = '';
        document.getElementById('key-password').value = '';
        document.getElementById('decrypt-password').value = '';
        document.getElementById('decrypt-output').value = '';
        
        // Reset file name displays
        document.querySelectorAll('#decrypt .file-name').forEach(span => {
            span.textContent = 'No file selected';
        });
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});
