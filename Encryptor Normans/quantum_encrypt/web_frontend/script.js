// Global variables
let currentTab = 'encrypt';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeFormHandlers();
    initializeFileInputs();
});

// Tab Management
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            switchTab(tabId);
        });
    });
}

function switchTab(tabId) {
    // Update buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // Update panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    document.getElementById(tabId).classList.add('active');

    currentTab = tabId;
}

// Form Handlers
function initializeFormHandlers() {
    // Encrypt type handlers
    document.querySelectorAll('input[name="encrypt-type"]').forEach(radio => {
        radio.addEventListener('change', handleEncryptTypeChange);
    });

    // Decrypt type handlers
    document.querySelectorAll('input[name="decrypt-type"]').forEach(radio => {
        radio.addEventListener('change', handleDecryptTypeChange);
    });

    // Encrypt method handlers
    document.querySelectorAll('input[name="encrypt-method"]').forEach(radio => {
        radio.addEventListener('change', handleEncryptMethodChange);
    });

    // Decrypt method handlers
    document.querySelectorAll('input[name="decrypt-method"]').forEach(radio => {
        radio.addEventListener('change', handleDecryptMethodChange);
    });
}

function handleEncryptTypeChange(event) {
    const type = event.target.value;
    const fileGroup = document.getElementById('file-input-group');
    const textGroup = document.getElementById('text-input-group');
    const compressionGroup = document.getElementById('compression-group');
    const fileInput = document.getElementById('encrypt-input');

    if (type === 'text') {
        fileGroup.classList.add('hidden');
        textGroup.classList.remove('hidden');
        compressionGroup.classList.add('hidden');
    } else {
        fileGroup.classList.remove('hidden');
        textGroup.classList.add('hidden');

        if (type === 'folder') {
            compressionGroup.classList.remove('hidden');
            // Enable directory selection for folders
            fileInput.setAttribute('webkitdirectory', '');
            fileInput.setAttribute('directory', '');
            fileInput.setAttribute('multiple', '');
        } else {
            compressionGroup.classList.add('hidden');
            // Remove directory attributes for files
            fileInput.removeAttribute('webkitdirectory');
            fileInput.removeAttribute('directory');
            fileInput.removeAttribute('multiple');
        }
    }
}

function handleDecryptTypeChange(event) {
    const type = event.target.value;
    const fileGroup = document.getElementById('decrypt-file-input-group');
    const textGroup = document.getElementById('decrypt-text-input-group');

    if (type === 'text') {
        fileGroup.classList.add('hidden');
        textGroup.classList.remove('hidden');
    } else {
        fileGroup.classList.remove('hidden');
        textGroup.classList.add('hidden');
    }
}

function handleEncryptMethodChange(event) {
    const method = event.target.value;
    const publicKeyGroup = document.getElementById('public-key-group');
    const passwordGroup = document.getElementById('password-group');

    if (method === 'password') {
        publicKeyGroup.classList.add('hidden');
        passwordGroup.classList.remove('hidden');
    } else {
        publicKeyGroup.classList.remove('hidden');
        passwordGroup.classList.add('hidden');
    }
}

function handleDecryptMethodChange(event) {
    const method = event.target.value;
    const privateKeyGroup = document.getElementById('private-key-group');
    const passwordGroup = document.getElementById('decrypt-password-group');

    if (method === 'password') {
        privateKeyGroup.classList.add('hidden');
        passwordGroup.classList.remove('hidden');
    } else {
        privateKeyGroup.classList.remove('hidden');
        passwordGroup.classList.add('hidden');
    }
}

// File Input Handlers
function initializeFileInputs() {
    const fileInputs = document.querySelectorAll('.file-input');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            let displayText = 'No file selected';

            if (this.files && this.files.length > 0) {
                if (this.files.length === 1) {
                    displayText = this.files[0].name;
                } else {
                    // Multiple files (folder selection)
                    displayText = `${this.files.length} files selected`;

                    // Try to get folder name from first file path
                    const firstFile = this.files[0];
                    if (firstFile.webkitRelativePath) {
                        const folderName = firstFile.webkitRelativePath.split('/')[0];
                        displayText = `Folder: ${folderName} (${this.files.length} files)`;
                    }
                }
            }

            const fileNameSpan = this.parentElement.querySelector('.file-name');
            if (fileNameSpan) {
                fileNameSpan.textContent = displayText;
            }
        });
    });
}

// Password Utilities
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        button.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        button.className = 'fas fa-eye';
    }
}

function generatePassword() {
    const password = generateSecurePasswordString(32);
    document.getElementById('encrypt-password').value = password;
}

async function generateSecurePassword() {
    const length = parseInt(document.getElementById('password-length').value);
    const includeUppercase = document.getElementById('include-uppercase').checked;
    const includeLowercase = document.getElementById('include-lowercase').checked;
    const includeNumbers = document.getElementById('include-numbers').checked;
    const includeSymbols = document.getElementById('include-symbols').checked;

    try {
        const result = await apiRequest('generate-password', {
            length: length,
            include_uppercase: includeUppercase,
            include_lowercase: includeLowercase,
            include_numbers: includeNumbers,
            include_symbols: includeSymbols
        });

        document.getElementById('generated-password').value = result.password;
    } catch (error) {
        // Fallback to client-side generation
        console.log('Using client-side password generation:', error.message);
        const password = generateSecurePasswordString(length, {
            uppercase: includeUppercase,
            lowercase: includeLowercase,
            numbers: includeNumbers,
            symbols: includeSymbols
        });

        document.getElementById('generated-password').value = password;

        // Show a subtle notification that this is client-side generation
        if (error.message.includes('Demo Mode')) {
            showInfo('Password Generated', 'Password generated using client-side cryptographically secure random generator. For server-side generation, start the web server.');
        }
    }
}

function generateSecurePasswordString(length, options = {}) {
    const defaults = {
        uppercase: true,
        lowercase: true,
        numbers: true,
        symbols: true
    };
    
    const opts = { ...defaults, ...options };
    
    let charset = '';
    if (opts.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (opts.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (opts.numbers) charset += '0123456789';
    if (opts.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    if (charset === '') {
        showError('Please select at least one character type');
        return '';
    }
    
    let password = '';
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        showSuccess('Copied to clipboard!');
    } catch (err) {
        showError('Failed to copy to clipboard');
    }
}

// Main Operations
async function performEncryption() {
    const type = document.querySelector('input[name="encrypt-type"]:checked').value;
    const method = document.querySelector('input[name="encrypt-method"]:checked').value;

    // Validate inputs
    if (!validateEncryptionInputs(type, method)) {
        return;
    }

    // Execute encryption
    showProgress('Encrypting...', 'Preparing encryption...');

    try {
        let result;

        if (type === 'text') {
            // For text, use JSON API
            const requestData = buildEncryptionRequest(type, method);

            // If using public key for text encryption, read the key content
            if (method === 'public-key' && requestData.needs_key_content) {
                const publicKeyInput = document.getElementById('public-key');
                if (publicKeyInput.files && publicKeyInput.files[0]) {
                    const keyContent = await readFileAsText(publicKeyInput.files[0]);
                    requestData.public_key_content = btoa(keyContent); // Base64 encode
                }
                delete requestData.needs_key_content; // Remove helper flag
            }

            result = await apiRequest('encrypt', requestData);
            hideProgress(); // Text encryption is fast, hide immediately
        } else {
            // For files/folders, use file upload API with progress tracking
            result = await uploadAndEncrypt(type, method);

            // Progress tracking is started in uploadAndEncrypt, don't hide yet
            // hideProgress will be called when progress tracking completes
        }

        // Only show result if progress is not being tracked
        if (type === 'text') {
            showTextEncryptionResult(result);
        } else if (!result.operation_id) {
            showEncryptionResult(result);
        } else {
            // For file operations with progress tracking, result will be shown when complete
            console.log('Progress tracking started for operation:', result.operation_id);
        }
    } catch (error) {
        hideProgress();
        showResult('Encryption Failed', error.message, 'error');
    }
}

async function performDecryption() {
    const type = document.querySelector('input[name="decrypt-type"]:checked').value;
    const method = document.querySelector('input[name="decrypt-method"]:checked').value;

    // Validate inputs
    if (!validateDecryptionInputs(type, method)) {
        return;
    }

    // Execute decryption
    showProgress('Decrypting...', 'Preparing decryption...');

    try {
        let result;

        if (type === 'text') {
            // For text, use JSON API
            const requestData = buildDecryptionRequest(type, method);

            // If using private key for text decryption, read the key content
            if (method === 'private-key' && requestData.needs_key_content) {
                const privateKeyInput = document.getElementById('private-key');
                if (privateKeyInput.files && privateKeyInput.files[0]) {
                    const keyContent = await readFileAsText(privateKeyInput.files[0]);
                    requestData.private_key_content = btoa(keyContent); // Base64 encode
                }
                delete requestData.needs_key_content; // Remove helper flag
            }

            result = await apiRequest('decrypt', requestData);
            hideProgress(); // Text decryption is fast, hide immediately
            showTextDecryptionResult(result);
        } else {
            // For files/folders, use file upload API with progress tracking
            result = await uploadAndDecrypt(type, method);

            // Progress tracking is started in uploadAndDecrypt, don't hide yet
            // hideProgress will be called when progress tracking completes
        }

        // Only show result if progress is not being tracked
        if (type === 'text' || !result.operation_id) {
            if (type !== 'text') {
                showDecryptionResult(result);
            }
        } else {
            // For file operations with progress tracking, result will be shown when complete
            console.log('Decryption progress tracking started for operation:', result.operation_id);
        }
    } catch (error) {
        hideProgress();
        showResult('Decryption Failed', error.message, 'error');
    }
}

async function generateKeys() {
    const securityLevel = document.getElementById('key-security-level').value;
    const publicKeyOutput = document.getElementById('public-key-output').value || 'public_key.key';
    const privateKeyOutput = document.getElementById('private-key-output').value || 'private_key.key';
    const keyPassword = document.getElementById('key-encryption-password').value;

    const requestData = {
        security_level: securityLevel,
        public_key_output: publicKeyOutput,
        private_key_output: privateKeyOutput,
        key_password: keyPassword || null
    };

    showProgress('Generating Keys...', 'Creating quantum-safe keypair...');

    try {
        const result = await apiRequest('generate-keys', requestData);
        hideProgress();
        showResult('Keys Generated Successfully', result.output, 'success');
    } catch (error) {
        hideProgress();
        showResult('Key Generation Failed', error.message, 'error');
    }
}

async function showKeyInfo() {
    const keyFile = document.getElementById('key-info-file').files[0];

    if (!keyFile) {
        showError('Please select a key file');
        return;
    }

    showProgress('Analyzing Key...', 'Reading key file information...');

    try {
        const formData = new FormData();
        formData.append('key_file', keyFile);

        const response = await fetch('/api/key-info', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        hideProgress();

        if (result.success) {
            if (result.data.status === 'valid') {
                showResult('Key Information', result.data.key_info, 'success');
            } else {
                showResult('Invalid Key', result.data.error, 'error');
            }
        } else {
            showResult('Key Analysis Failed', result.message, 'error');
        }
    } catch (error) {
        hideProgress();
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            showError('Demo Mode: Key analysis requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8083');
        } else {
            showError(`Key analysis failed: ${error.message}`);
        }
    }
}

// Request Building Functions
function buildEncryptionRequest(type, method) {
    const requestData = {
        operation_type: type,
        method: method,
        security_level: document.getElementById('security-level').value
    };

    // Add input data
    if (type === 'text') {
        requestData.input = document.getElementById('encrypt-text').value;
    } else {
        const fileInput = document.getElementById('encrypt-input');
        if (fileInput.files && fileInput.files.length > 0) {
            if (type === 'folder') {
                // For folders, use the folder name from the first file's path
                const firstFile = fileInput.files[0];
                if (firstFile.webkitRelativePath) {
                    requestData.input = firstFile.webkitRelativePath.split('/')[0];
                } else {
                    requestData.input = 'selected_folder';
                }
            } else {
                // For files, use the file name
                requestData.input = fileInput.files[0].name;
            }
        }
    }

    // Add compression level for folders
    if (type === 'folder') {
        requestData.compression_level = parseInt(document.getElementById('compression-level').value);
    }

    // Add encryption method details
    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        if (publicKey) {
            requestData.public_key_path = publicKey.name;
            // For text encryption, we need the actual key content
            if (type === 'text') {
                // This will be handled in the encrypt function
                requestData.needs_key_content = true;
            }
        }
    } else {
        requestData.password = document.getElementById('encrypt-password').value;
    }

    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        requestData.output_path = output;
    }

    return requestData;
}

function buildDecryptionRequest(type, method) {
    const requestData = {
        operation_type: type,
        method: method
    };

    // Add input data
    if (type === 'text') {
        requestData.input = document.getElementById('decrypt-text').value;
    } else {
        const fileInput = document.getElementById('decrypt-input').files[0];
        requestData.input = fileInput.name; // Note: In real implementation, file would be uploaded
    }

    // Add decryption method details
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        if (privateKey) {
            requestData.private_key_path = privateKey.name;
            // For text decryption, we need the actual key content
            if (type === 'text') {
                requestData.needs_key_content = true;
            }
        }

        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            requestData.key_password = keyPassword;
        }
    } else {
        requestData.password = document.getElementById('decrypt-password').value;
    }

    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        requestData.output_path = output;
    }

    return requestData;
}

// Legacy Command Building Functions (for reference)
function buildEncryptionCommand(type, method) {
    const command = ['./target/release/normans-quantum-proof-encryption'];
    
    // Add operation type
    if (type === 'file') {
        command.push('encrypt-file');
        const fileInput = document.getElementById('encrypt-input').files[0];
        command.push(fileInput.name); // Note: This is simplified - would need proper file handling
    } else if (type === 'folder') {
        command.push('encrypt-folder');
        const folderInput = document.getElementById('encrypt-input').files[0];
        command.push(folderInput.name);
    } else if (type === 'text') {
        command.push('encrypt-text');
        const text = document.getElementById('encrypt-text').value;
        command.push(`"${text}"`);
    }
    
    // Add security level
    const securityLevel = document.getElementById('security-level').value;
    command.push('--security', securityLevel);
    
    // Add encryption method
    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        command.push('--public-key', publicKey.name);
    } else {
        const password = document.getElementById('encrypt-password').value;
        command.push('--password');
        // Password would be provided via stdin
    }
    
    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        command.push('--output', output);
    }
    
    return command;
}

function buildDecryptionCommand(type, method) {
    const command = ['./target/release/normans-quantum-proof-encryption'];
    
    // Add operation type
    if (type === 'file') {
        command.push('decrypt-file');
        const fileInput = document.getElementById('decrypt-input').files[0];
        command.push(fileInput.name);
    } else if (type === 'folder') {
        command.push('decrypt-folder');
        const folderInput = document.getElementById('decrypt-input').files[0];
        command.push(folderInput.name);
    } else if (type === 'text') {
        command.push('decrypt-text');
        const text = document.getElementById('decrypt-text').value;
        command.push(`"${text}"`);
    }
    
    // Add decryption method
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        command.push('--private-key', privateKey.name);
        
        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            command.push('--key-password', keyPassword);
        }
    } else {
        const password = document.getElementById('decrypt-password').value;
        command.push('--password');
        // Password would be provided via stdin
    }
    
    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        command.push('--output', output);
    }
    
    return command;
}

// Validation Functions
function validateEncryptionInputs(type, method) {
    if (type === 'text') {
        const text = document.getElementById('encrypt-text').value;
        if (!text.trim()) {
            showError('Please enter text to encrypt');
            return false;
        }
    } else {
        const fileInput = document.getElementById('encrypt-input');
        if (!fileInput.files || fileInput.files.length === 0) {
            const itemType = type === 'folder' ? 'folder' : 'file';
            showError(`Please select a ${itemType} to encrypt`);
            return false;
        }
    }

    if (method === 'public-key') {
        const publicKey = document.getElementById('public-key').files[0];
        if (!publicKey) {
            showError('Please select a public key file');
            return false;
        }
    } else {
        const password = document.getElementById('encrypt-password').value;
        if (!password) {
            showError('Please enter a password');
            return false;
        }
    }

    return true;
}

function validateDecryptionInputs(type, method) {
    if (type === 'text') {
        const text = document.getElementById('decrypt-text').value;
        if (!text.trim()) {
            showError('Please enter encrypted text');
            return false;
        }
    } else {
        const fileInput = document.getElementById('decrypt-input').files[0];
        if (!fileInput) {
            showError('Please select an encrypted file or archive');
            return false;
        }
    }
    
    if (method === 'private-key') {
        const privateKey = document.getElementById('private-key').files[0];
        if (!privateKey) {
            showError('Please select a private key file');
            return false;
        }
    } else {
        const password = document.getElementById('decrypt-password').value;
        if (!password) {
            showError('Please enter a password');
            return false;
        }
    }
    
    return true;
}

// File Upload and Encryption
async function uploadAndEncrypt(type, method) {
    const fileInput = document.getElementById('encrypt-input');

    if (!fileInput.files || fileInput.files.length === 0) {
        throw new Error('No files selected');
    }

    // Check upload strategy based on size and file count
    const totalSize = Array.from(fileInput.files).reduce((sum, file) => sum + file.size, 0);
    const CHUNK_THRESHOLD = 500 * 1024 * 1024; // 500MB threshold for chunked upload
    const LARGE_FOLDER_THRESHOLD = 1024 * 1024 * 1024; // 1GB threshold for large folder upload

    if (totalSize > CHUNK_THRESHOLD && fileInput.files.length === 1) {
        // Use chunked upload for single large files
        return await uploadLargeFileChunked(fileInput.files[0], type, method);
    } else if (totalSize > LARGE_FOLDER_THRESHOLD && fileInput.files.length > 1) {
        // Use streaming upload for large folders with multiple files
        return await uploadLargeFolderStreaming(type, method);
    } else {
        // Use regular upload for smaller files or folders
        return await uploadRegular(type, method);
    }
}

// Regular upload for smaller files
async function uploadRegular(type, method) {
    const formData = new FormData();

    // Add form fields
    formData.append('operation_type', type);
    formData.append('method', method);
    formData.append('security_level', document.getElementById('security-level').value);

    // Add compression level for folders
    if (type === 'folder') {
        formData.append('compression_level', document.getElementById('compression-level').value);
    }

    // Add files
    const fileInput = document.getElementById('encrypt-input');
    if (fileInput.files && fileInput.files.length > 0) {
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('file', fileInput.files[i]);
        }
    }

    // Add encryption method details
    if (method === 'public-key') {
        const publicKeyInput = document.getElementById('public-key');
        if (publicKeyInput.files && publicKeyInput.files[0]) {
            formData.append('public_key', publicKeyInput.files[0]);
        }
    } else {
        formData.append('password', document.getElementById('encrypt-password').value);
    }

    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        formData.append('output_path', output);
    }

    // Upload and encrypt
    try {
        const response = await fetch('/api/encrypt-upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: File upload requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8080');
        }
        throw new Error(`Upload failed: ${error.message}`);
    }
}

// Chunked upload for large files (20GB+)
async function uploadLargeFileChunked(file, type, method) {
    const CHUNK_SIZE = 50 * 1024 * 1024; // 50MB chunks
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    updateProgress(`Uploading large file: ${file.name}`, `Preparing ${totalChunks} chunks...`);

    try {
        // Upload chunks
        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
            const start = chunkIndex * CHUNK_SIZE;
            const end = Math.min(start + CHUNK_SIZE, file.size);
            const chunk = file.slice(start, end);

            const formData = new FormData();
            formData.append('upload_id', uploadId);
            formData.append('chunk_index', chunkIndex.toString());
            formData.append('total_chunks', totalChunks.toString());
            formData.append('filename', file.name);
            formData.append('file_size', file.size.toString());
            formData.append('operation_type', type);
            formData.append('method', method);
            formData.append('chunk_data', chunk);

            const response = await fetch('/api/chunk-upload', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Chunk upload failed: HTTP ${response.status}`);
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error(`Chunk upload failed: ${result.message}`);
            }

            // Update progress
            const progress = ((chunkIndex + 1) / totalChunks * 80); // 80% for upload, 20% for processing
            updateProgress(
                `Uploading large file: ${file.name}`,
                `Chunk ${chunkIndex + 1}/${totalChunks} uploaded (${progress.toFixed(1)}%)`
            );
        }

        // Complete the upload and process
        updateProgress(`Processing large file: ${file.name}`, 'Assembling chunks and encrypting...');

        const completeRequest = {
            upload_id: uploadId,
            operation_type: type,
            method: method,
            security_level: document.getElementById('security-level').value,
            compression_level: type === 'folder' ? parseInt(document.getElementById('compression-level').value) : null,
            password: method === 'password' ? document.getElementById('encrypt-password').value : null,
            output_path: document.getElementById('encrypt-output').value || null
        };

        const completeResponse = await fetch('/api/chunk-complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(completeRequest)
        });

        if (!completeResponse.ok) {
            throw new Error(`Processing failed: HTTP ${completeResponse.status}`);
        }

        const completeResult = await completeResponse.json();
        if (!completeResult.success) {
            throw new Error(`Processing failed: ${completeResult.message}`);
        }

        return completeResult.data;

    } catch (error) {
        throw new Error(`Large file upload failed: ${error.message}`);
    }
}

// Large folder streaming upload for folders with many files
async function uploadLargeFolderStreaming(type, method) {
    const formData = new FormData();
    const fileInput = document.getElementById('encrypt-input');

    // Add form fields
    formData.append('method', method);
    formData.append('security_level', document.getElementById('security-level').value);
    formData.append('compression_level', document.getElementById('compression-level').value);

    // Add encryption method details
    if (method === 'public-key') {
        const publicKeyInput = document.getElementById('public-key');
        if (publicKeyInput.files && publicKeyInput.files[0]) {
            formData.append('public_key', publicKeyInput.files[0]);
        }
    } else {
        formData.append('password', document.getElementById('encrypt-password').value);
    }

    // Add output path if specified
    const output = document.getElementById('encrypt-output').value;
    if (output) {
        formData.append('output_path', output);
    }

    // Add all files with progress tracking
    const files = Array.from(fileInput.files);
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    updateProgress(
        `Uploading large folder: ${files.length} files`,
        `Preparing ${formatBytes(totalSize)} of media files...`
    );

    // Add files to form data
    for (let i = 0; i < files.length; i++) {
        formData.append('file', files[i]);

        // Update progress every 10 files
        if (i % 10 === 0) {
            updateProgress(
                `Uploading large folder: ${files.length} files`,
                `Added ${i + 1}/${files.length} files to upload queue...`
            );
            // Allow UI to update
            await new Promise(resolve => setTimeout(resolve, 10));
        }
    }

    updateProgress(
        `Processing large folder: ${files.length} files`,
        'Streaming files to server and encrypting...'
    );

    // Upload with streaming support
    try {
        const response = await fetch('/api/large-folder-upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        // Start progress tracking if operation ID is provided
        if (result.data.operation_id) {
            startProgressTracking(result.data.operation_id);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: Large folder upload requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8085');
        }
        throw new Error(`Large folder upload failed: ${error.message}`);
    }
}

// Enhanced progress tracking with real-time updates
let progressInterval = null;
let currentOperationId = null;

function startProgressTracking(operationId) {
    currentOperationId = operationId;

    if (progressInterval) {
        clearInterval(progressInterval);
    }

    progressInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/progress/${operationId}`);
            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    updateProgressDisplay(result.data);

                    // Stop tracking when complete and show results
                    if (result.data.status === 'complete' || result.data.percentage >= 100) {
                        clearInterval(progressInterval);
                        progressInterval = null;

                        // Hide progress modal and show results after a brief delay
                        setTimeout(() => {
                            hideProgress();

                            // Show appropriate result based on operation type
                            if (result.data.operation_type === 'encrypt') {
                                // Get the result data from the last operation
                                showEncryptionResult({
                                    message: 'Encryption completed successfully!',
                                    operation_id: result.data.operation_id,
                                    // Add other result data as needed
                                });
                            } else if (result.data.operation_type === 'decrypt') {
                                showDecryptionResult({
                                    message: 'Decryption completed successfully!',
                                    operation_id: result.data.operation_id,
                                    // Add other result data as needed
                                });
                            }
                        }, 1000); // 1 second delay to show 100% completion
                    }
                }
            }
        } catch (error) {
            console.log('Progress tracking error:', error);
        }
    }, 1000); // Update every second
}

function updateProgressDisplay(progressData) {
    console.log('Updating progress display with:', progressData);

    // Update title
    const titleElement = document.getElementById('progress-title');
    if (titleElement) {
        const icon = progressData.stage === 'complete' ? 'fa-check' : 'fa-cog fa-spin';
        const operationType = progressData.operation_type || 'OPERATION';
        const stage = progressData.stage || 'PROCESSING';
        titleElement.innerHTML = `<i class="fas ${icon}"></i> ${operationType.toUpperCase()}: ${stage.toUpperCase()}`;
    }

    // Update progress bar
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');
    if (progressFill && progressPercentage) {
        const percentage = progressData.percentage || 0;
        progressFill.style.width = `${percentage}%`;
        progressPercentage.textContent = `${percentage.toFixed(1)}%`;
    }

    // Update current file
    const progressText = document.getElementById('progress-text');
    if (progressText) {
        const currentFile = progressData.current_file || 'Processing...';
        progressText.textContent = `📁 ${currentFile}`;
    }

    // Update statistics with real data
    const filesProgress = document.getElementById('files-progress');
    if (filesProgress) {
        const processed = progressData.files_processed || 0;
        const total = progressData.total_files || 0;
        filesProgress.textContent = `${processed}/${total}`;
    }

    const sizeProgress = document.getElementById('size-progress');
    if (sizeProgress) {
        const processed = progressData.bytes_processed || 0;
        const total = progressData.total_bytes || 0;
        sizeProgress.textContent = `${formatBytes(processed)} / ${formatBytes(total)}`;
    }

    const speedProgress = document.getElementById('speed-progress');
    if (speedProgress) {
        if (progressData.current_speed && progressData.current_speed > 0) {
            speedProgress.textContent = `${progressData.current_speed.toFixed(1)} MB/s`;
        } else {
            speedProgress.textContent = '-- MB/s';
        }
    }

    const timeRemaining = document.getElementById('time-remaining');
    if (timeRemaining) {
        if (progressData.estimated_time_remaining && progressData.estimated_time_remaining > 0) {
            timeRemaining.textContent = formatTime(progressData.estimated_time_remaining);
        } else if (progressData.stage === 'complete') {
            timeRemaining.textContent = 'Complete';
        } else {
            timeRemaining.textContent = 'Calculating...';
        }
    }

    const elapsedTime = document.getElementById('elapsed-time');
    if (elapsedTime) {
        // Calculate elapsed time from started_at if available
        let elapsed = 0;
        if (progressData.started_at) {
            const startTime = new Date(progressData.started_at);
            const now = new Date();
            elapsed = Math.floor((now - startTime) / 1000);
        } else if (progressData.elapsed_time) {
            elapsed = progressData.elapsed_time;
        }
        elapsedTime.textContent = formatTime(elapsed);
    }
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}

// Helper function to update progress during chunked upload
function updateProgress(title, message) {
    const progressModal = document.getElementById('progress-modal');
    const titleElement = progressModal.querySelector('h3');
    const messageElement = document.getElementById('progress-text');

    if (titleElement) {
        titleElement.innerHTML = `<i class="fas fa-upload fa-spin"></i> ${title}`;
    }
    if (messageElement) {
        messageElement.textContent = message;
    }
}

// Helper function to read file as text
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('Failed to read file'));
        reader.readAsText(file);
    });
}

// File Upload and Decryption with intelligent routing
async function uploadAndDecrypt(type, method) {
    const fileInput = document.getElementById('decrypt-input');

    if (!fileInput.files || fileInput.files.length === 0) {
        throw new Error('No files selected for decryption');
    }

    // Check file size to determine upload strategy
    const totalSize = Array.from(fileInput.files).reduce((sum, file) => sum + file.size, 0);
    const LARGE_FILE_THRESHOLD = 1024 * 1024 * 1024; // 1GB threshold

    if (totalSize > LARGE_FILE_THRESHOLD) {
        // Use large file decryption endpoint for files > 1GB
        return await uploadLargeFileDecryption(type, method);
    } else {
        // Use streaming decryption for smaller files
        return await uploadStreamingDecryption(type, method);
    }
}

// Streaming decryption for medium files (< 1GB)
async function uploadStreamingDecryption(type, method) {
    const formData = new FormData();

    // Add form fields
    formData.append('operation_type', type);
    formData.append('method', method);

    // Add encrypted files
    const fileInput = document.getElementById('decrypt-input');
    if (fileInput.files && fileInput.files.length > 0) {
        for (let i = 0; i < fileInput.files.length; i++) {
            formData.append('encrypted_file', fileInput.files[i]);
        }
    }

    // Add decryption method details
    if (method === 'private-key') {
        const privateKeyInput = document.getElementById('private-key');
        if (privateKeyInput.files && privateKeyInput.files[0]) {
            formData.append('private_key', privateKeyInput.files[0]);
        }

        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            formData.append('key_password', keyPassword);
        }
    } else {
        formData.append('password', document.getElementById('decrypt-password').value);
    }

    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        formData.append('output_path', output);
    }

    // Upload and decrypt with streaming
    try {
        const response = await fetch('/api/decrypt-upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        // Start progress tracking if operation ID is provided
        if (result.data.operation_id) {
            startProgressTracking(result.data.operation_id);
        }

        return result.data;
    } catch (error) {
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: File decryption requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8090');
        }
        throw new Error(`Decryption failed: ${error.message}`);
    }
}

// Large file decryption for files > 1GB
async function uploadLargeFileDecryption(type, method) {
    const formData = new FormData();
    const fileInput = document.getElementById('decrypt-input');
    const file = fileInput.files[0]; // Assume single large file

    updateProgress(
        `Decrypting large file: ${file.name}`,
        `Preparing ${formatBytes(file.size)} encrypted file...`
    );

    // Add form fields
    formData.append('operation_type', type);
    formData.append('method', method);
    formData.append('encrypted_file', file);

    // Add decryption method details
    if (method === 'private-key') {
        const privateKeyInput = document.getElementById('private-key');
        if (privateKeyInput.files && privateKeyInput.files[0]) {
            formData.append('private_key', privateKeyInput.files[0]);
        }

        const keyPassword = document.getElementById('key-password').value;
        if (keyPassword) {
            formData.append('key_password', keyPassword);
        }
    } else {
        formData.append('password', document.getElementById('decrypt-password').value);
    }

    // Add output path if specified
    const output = document.getElementById('decrypt-output').value;
    if (output) {
        formData.append('output_path', output);
    }

    // Upload and decrypt large file
    try {
        updateProgress(
            `Decrypting large file: ${file.name}`,
            'Uploading and processing large encrypted file...'
        );

        const response = await fetch('/api/large-decrypt-upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        // Start progress tracking if operation ID is provided
        if (result.data.operation_id) {
            startProgressTracking(result.data.operation_id);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: File decryption requires the web server. Start with: ./target/release/normans-quantum-proof-encryption --web --port 8083');
        }
        throw new Error(`Decryption failed: ${error.message}`);
    }
}

// API Communication
async function apiRequest(endpoint, data) {
    try {
        const response = await fetch(`/api/${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message);
        }

        return result.data;
    } catch (error) {
        // If API is not available, show demo message
        if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
            throw new Error('Demo Mode: This is a demonstration of the web interface. To enable full functionality, start the program with: ./target/release/normans-quantum-proof-encryption --web --port 8080');
        }
        throw new Error(`API request failed: ${error.message}`);
    }
}

// UI Helper Functions
function showProgress(title, message) {
    document.getElementById('progress-modal').classList.add('show');

    // Initialize progress display
    const titleElement = document.getElementById('progress-title');
    const progressText = document.getElementById('progress-text');
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');

    if (titleElement) {
        titleElement.innerHTML = `<i class="fas fa-cog fa-spin"></i> ${title}`;
    }
    if (progressText) {
        progressText.textContent = message;
    }
    if (progressFill) {
        progressFill.style.width = '0%';
    }
    if (progressPercentage) {
        progressPercentage.textContent = '0%';
    }

    // Reset all progress stats to initial values
    resetProgressStats();
}

function resetProgressStats() {
    const elements = {
        'files-progress': '0/0',
        'size-progress': '0 MB / 0 MB',
        'speed-progress': '-- MB/s',
        'time-remaining': 'Calculating...',
        'elapsed-time': '0s'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

function hideProgress() {
    document.getElementById('progress-modal').classList.remove('show');

    // Reset progress bar
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');
    if (progressFill) {
        progressFill.style.width = '0%';
    }
    if (progressPercentage) {
        progressPercentage.textContent = '0%';
    }

    // Stop any active progress tracking
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
    currentOperationId = null;

    // Reset progress stats
    resetProgressStats();
}

function showResult(title, message, type) {
    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');
    
    titleElement.innerHTML = type === 'success' 
        ? `<i class="fas fa-check-circle" style="color: #28a745;"></i> ${title}`
        : `<i class="fas fa-exclamation-circle" style="color: #dc3545;"></i> ${title}`;
    
    messageElement.textContent = message;
    outputElement.textContent = message;
    
    modal.classList.add('show');
}

function showError(message) {
    showResult('Error', message, 'error');
}

function showSuccess(message) {
    showResult('Success', message, 'success');
}

function showInfo(title, message) {
    showResult(title, message, 'info');
}

// Text-specific result display functions
function showTextEncryptionResult(result) {
    // Debug: Log the result structure
    console.log('Text encryption result:', result);

    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');

    titleElement.innerHTML = `<i class="fas fa-lock" style="color: #28a745;"></i> Text Encryption Successful`;

    // Create text-specific result message
    let message = `Your text has been encrypted successfully!\n\n`;
    message += `📝 Operation: Text Encryption\n`;
    message += `🔒 Method: ${result.method || 'Password-based'}\n`;
    message += `✅ Status: Complete`;

    messageElement.textContent = message;

    // Create encrypted text display section
    const textSection = document.createElement('div');
    textSection.className = 'text-result-section';
    textSection.innerHTML = `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">
                <i class="fas fa-clipboard"></i> Encrypted Text
            </h4>
            <p style="margin: 0 0 10px 0; color: #666;">
                Copy the encrypted text below and store it securely:
            </p>
            <textarea id="encrypted-text-result" readonly style="width: 100%; height: 120px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px; background: #fff;">${result.output || 'Encrypted text not available'}</textarea>
            <div style="margin-top: 10px;">
                <button class="btn btn-secondary" onclick="copyToClipboard('encrypted-text-result')">
                    <i class="fas fa-copy"></i> Copy to Clipboard
                </button>
            </div>
        </div>
    `;

    outputElement.innerHTML = '';
    outputElement.appendChild(textSection);

    modal.classList.add('show');
}

function showTextDecryptionResult(result) {
    // Debug: Log the result structure
    console.log('Text decryption result:', result);

    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');

    titleElement.innerHTML = `<i class="fas fa-unlock" style="color: #28a745;"></i> Text Decryption Successful`;

    // Create text-specific result message
    let message = `Your text has been decrypted successfully!\n\n`;
    message += `📝 Operation: Text Decryption\n`;
    message += `🔓 Method: ${result.method || 'Password-based'}\n`;
    message += `✅ Status: Complete`;

    messageElement.textContent = message;

    // Create decrypted text display section
    const textSection = document.createElement('div');
    textSection.className = 'text-result-section';
    textSection.innerHTML = `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">
                <i class="fas fa-file-alt"></i> Decrypted Text
            </h4>
            <p style="margin: 0 0 10px 0; color: #666;">
                Your original text has been recovered:
            </p>
            <textarea id="decrypted-text-result" readonly style="width: 100%; height: 120px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: inherit; font-size: 14px; background: #fff;">${result.output || 'Decrypted text not available'}</textarea>
            <div style="margin-top: 10px;">
                <button class="btn btn-secondary" onclick="copyToClipboard('decrypted-text-result')">
                    <i class="fas fa-copy"></i> Copy to Clipboard
                </button>
            </div>
        </div>
    `;

    outputElement.innerHTML = '';
    outputElement.appendChild(textSection);

    modal.classList.add('show');
}

// File-specific result display function
function showEncryptionResult(result) {
    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');

    titleElement.innerHTML = `<i class="fas fa-check-circle" style="color: #28a745;"></i> Encryption Successful`;

    // Create detailed result message
    let message = `${result.message}\n\n`;
    message += `📁 Files processed: ${result.original_files}\n`;
    message += `📊 Original size: ${formatBytes(result.original_size)}\n`;
    message += `🔒 Encrypted size: ${formatBytes(result.encrypted_size)}\n`;
    message += `📉 Compression: ${result.compression_ratio}\n`;
    message += `📦 ${result.compression_info}\n\n`;
    message += `📄 Output file: ${result.output_filename}`;

    messageElement.textContent = message;

    // Create download section
    const downloadSection = document.createElement('div');
    downloadSection.className = 'download-section';
    downloadSection.innerHTML = `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">
                <i class="fas fa-download"></i> Download Encrypted File
            </h4>
            <p style="margin: 0 0 15px 0; color: #666;">
                Your encrypted file is ready for download. Click the button below to save it locally.
            </p>
            <button class="btn btn-primary" onclick="downloadFile('${result.download_id}', '${result.output_filename}')">
                <i class="fas fa-download"></i> Download ${result.output_filename}
            </button>
            <p style="margin: 15px 0 0 0; font-size: 0.9em; color: #666;">
                <i class="fas fa-clock"></i> Download link expires in 1 hour
            </p>
        </div>
    `;

    outputElement.innerHTML = '';
    outputElement.appendChild(downloadSection);

    modal.classList.add('show');
}

function showDecryptionResult(result) {
    const modal = document.getElementById('result-modal');
    const titleElement = document.getElementById('result-title');
    const messageElement = document.getElementById('result-message');
    const outputElement = document.getElementById('result-output');

    titleElement.innerHTML = `<i class="fas fa-unlock" style="color: #28a745;"></i> Decryption Successful`;

    // Create detailed result message
    let message = `${result.message}\n\n`;
    message += `🔒 Encrypted size: ${formatBytes(result.encrypted_size)}\n`;
    message += `📂 Decrypted size: ${formatBytes(result.decrypted_size)}\n`;
    message += `📄 Output file: ${result.output_filename}\n`;
    message += `📦 Type: ${result.type === 'folder' ? 'Folder Archive (ZIP)' : 'Single File'}`;

    messageElement.textContent = message;

    // Create download section
    const downloadSection = document.createElement('div');
    downloadSection.className = 'download-section';
    downloadSection.innerHTML = `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
            <h4 style="margin: 0 0 10px 0; color: #28a745;">
                <i class="fas fa-download"></i> Download Decrypted ${result.type === 'folder' ? 'Archive' : 'File'}
            </h4>
            <p style="margin: 0 0 15px 0; color: #666;">
                Your decrypted ${result.type === 'folder' ? 'files have been packaged into a ZIP archive' : 'file is ready'}. Click the button below to download.
            </p>
            <button class="btn btn-primary" onclick="downloadFile('${result.download_id}', '${result.output_filename}')">
                <i class="fas fa-download"></i> Download ${result.output_filename}
            </button>
            <p style="margin: 15px 0 0 0; font-size: 0.9em; color: #666;">
                <i class="fas fa-clock"></i> Download link expires in 1 hour
            </p>
        </div>
    `;

    outputElement.innerHTML = '';
    outputElement.appendChild(downloadSection);

    modal.classList.add('show');
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function downloadFile(downloadId, filename) {
    try {
        const response = await fetch(`/api/download/${downloadId}`);

        if (!response.ok) {
            throw new Error(`Download failed: ${response.statusText}`);
        }

        const blob = await response.blob();

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showSuccess('File downloaded successfully!');
    } catch (error) {
        showError(`Download failed: ${error.message}`);
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Copy to clipboard function
function copyToClipboard(textAreaId) {
    const textArea = document.getElementById(textAreaId);
    if (!textArea) {
        console.error('Textarea not found:', textAreaId);
        alert('Failed to copy to clipboard. Please select and copy manually.');
        return;
    }

    textArea.select();
    textArea.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');

        // Show feedback - find the button that called this function
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.style.background = '#28a745';
        button.disabled = true;

        setTimeout(() => {
            button.innerHTML = originalText;
            button.style.background = '';
            button.disabled = false;
        }, 2000);

        console.log('Text copied to clipboard successfully');
    } catch (err) {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy to clipboard. Please select and copy manually.');
    }
}

function clearForm(formType) {
    if (formType === 'encrypt') {
        document.getElementById('encrypt-input').value = '';
        document.getElementById('encrypt-text').value = '';
        document.getElementById('public-key').value = '';
        document.getElementById('encrypt-password').value = '';
        document.getElementById('encrypt-output').value = '';
        
        // Reset file name displays
        document.querySelectorAll('#encrypt .file-name').forEach(span => {
            span.textContent = 'No file selected';
        });
    } else if (formType === 'decrypt') {
        document.getElementById('decrypt-input').value = '';
        document.getElementById('decrypt-text').value = '';
        document.getElementById('private-key').value = '';
        document.getElementById('key-password').value = '';
        document.getElementById('decrypt-password').value = '';
        document.getElementById('decrypt-output').value = '';
        
        // Reset file name displays
        document.querySelectorAll('#decrypt .file-name').forEach(span => {
            span.textContent = 'No file selected';
        });
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});
