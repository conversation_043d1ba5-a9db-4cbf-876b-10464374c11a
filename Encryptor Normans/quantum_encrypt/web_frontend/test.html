<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test - Normans Quantum-Proof Encryption</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>CSS Test - Normans Quantum-Proof Encryption</h1>
                </div>
                <div class="version">CSS Loading Test</div>
            </div>
        </header>
        
        <main class="main-content">
            <div class="card">
                <h2>CSS Loading Test</h2>
                <p>If you can see beautiful styling with gradients and glass effects, the CSS is loading correctly!</p>
                
                <div class="form-group">
                    <label>Test Input:</label>
                    <input type="text" placeholder="This should be styled">
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary">
                        <i class="fas fa-check"></i> Primary Button
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-times"></i> Secondary Button
                    </button>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JavaScript is working!');
            alert('JavaScript is working! If you see beautiful styling, CSS is also working.');
        });
    </script>
</body>
</html>
